{"name": "@clipnest/render", "version": "0.1.0", "private": true, "scripts": {"start": "tsx server.ts", "start:prod": "node dist/server.js", "build": "next build", "build:server": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json", "build:all": "npm run build:server && npm run build", "clean": "rimraf dist .next", "docker:build": "docker build -t clipnest-render .", "docker:run": "docker run -p 3000:3000 clipnest-render", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:dev:build": "docker-compose -f docker-compose.dev.yml up --build", "lint": "next lint", "deploy": "node deploy.mjs"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@remotion/bundler": "v4.0.272", "@remotion/captions": "4.0.272", "@remotion/cli": "4.0.272", "@remotion/cloudrun": "v4.0.272", "@remotion/google-fonts": "4.0.272", "@remotion/lambda": "4.0.272", "@remotion/player": "v4.0.272", "@remotion/renderer": "v4.0.272", "@remotion/studio": "v4.0.272", "@tanstack/react-query": "^5.82.0", "ali-oss": "^6.23.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cos-nodejs-sdk-v5": "^2.16.0-beta.3", "date-fns": "^4.1.0", "framer-motion": "^11.5.6", "gray-matter": "^4.0.3", "jsdom": "^26.1.0", "lodash": "^4.17.21", "lucide-react": "^0.438.0", "next": "^15.4.5", "next-themes": "^0.4.4", "opentype.js": "^1.3.4", "react": "^19.1.0", "react-best-gradient-color-picker": "^3.0.14", "react-dom": "^19.1.0", "react-hotkeys-hook": "^4.6.1", "remotion": "4.0.272", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tsconfig-paths-webpack-plugin": "^4.2.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/config-array": "^0.19.1", "@eslint/object-schema": "^2.1.5", "@types/ali-oss": "^6.16.11", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.12", "@types/node": "^20.19.9", "@types/opentype.js": "^1.3.8", "@types/react": "^18.3.18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8.57.1", "eslint-config-next": "14.2.8", "glob": "^11.0.1", "identity-obj-proxy": "^3.0.0", "lru-cache": "^11.0.2", "node-loader": "^2.1.0", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsx": "^4.20.3", "typescript": "^5.8.3"}}