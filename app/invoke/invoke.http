### render
POST http://localhost:3000/invoke
Content-Type: application/json
x-fc-request-id: 1234

{
  "renderConfig": "",
  "renderJson": "http://************:48080/app-api/creative/oss/object/d5630ba4b3034ea7b97a3226c5d87dab",
  "taskName": "0.0%-20250814",
  "taskNo": "0aa62b0864f9474c9bba30d57e894159",
  "tenantId": 1,
  "tenantName": "官方团队",
  "token": "test1",
  "uploadUrl": "http://************:48080/app-api/creative/oss/video/upload-param",
  "userId": 1,
  "messageUrl": "http://************:48080/client-api/creative/message"
}

### render
POST https://render-test-fkiipoieam.cn-hangzhou.fcapp.run/invoke
Content-Type: application/json
x-fc-request-id: 1234
X-Fc-Invocation-Type: Async

{
  "renderConfig": "",
  "renderJson": "http://************:48080/app-api/creative/oss/object/d5630ba4b3034ea7b97a3226c5d87dab",
  "taskName": "0.0%-20250814-1745",
  "taskNo": "0aa62b0864f9474c9bba30d57e894163",
  "tenantId": 1,
  "tenantName": "官方团队",
  "token": "test1",
  "uploadUrl": "http://************:48080/app-api/creative/oss/video/upload-param",
  "userId": 1
}