import {NextRequest, NextResponse} from 'next/server';
import {getRenderProgress} from "@/lib/ssr-helpers/custom-renderer";
import axios from "axios";
import {RenderRequestPayloadSchema} from "@/lib/types";

/**
 * POST endpoint
 */
export async function POST(request: NextRequest) {
    const rid = request.headers.get('x-fc-request-id') || '';
    const mqBody = await request.json();

    console.log(`FC invoke Start RequestId: ${rid}`);
    console.log('body:', mqBody)
    // do your things
    console.log(`FC invoke End RequestId: ${rid}`);

    if (!mqBody.renderJson) {
        console.warn('No renderJson found in message, skipping...');
        return;
    }
    if (!mqBody.taskNo) {
        console.warn('No taskNo found in message, skipping...');
        return;
    }
    if (!mqBody.tenantId) {
        console.warn('No tenantId found in message, skipping...');
        return;
    }
    if (!mqBody.uploadUrl) {
        console.warn('No uploadUrl found in message, skipping...');
        return;
    }
    if (!mqBody.token) {
        console.warn('No token found in message, skipping...');
        return;
    }
    const fetchUrlResult = await axios.get(mqBody.renderJson, {
        headers: {
            Authorization: `Bearer ${mqBody.token}`,
            'tenant-id': mqBody.tenantId,
        }
    });
    console.log('fetchUrlResult:', fetchUrlResult.data);
    if (fetchUrlResult.data.code !== 0) {
        throw new Error(`Failed to fetch STS: ${fetchUrlResult.data.msg}`);
    }
    const jsonUrl = fetchUrlResult.data.data;
    console.log('jsonUrl:', jsonUrl);
    try {
        const response = await axios.get(jsonUrl, {timeout: 5000});
        const body = response.data;

        console.log('Received body:', JSON.stringify(body, null, 2));

        const payload = await RenderRequestPayloadSchema.parseAsync({
            ...body,
            ...mqBody,
            taskId: mqBody.taskNo,
        });

        // 动态导入以避免构建时的问题
        const {startRendering} = await import("@/lib/ssr-helpers/custom-renderer");
        const renderId = await startRendering(payload);
        await waitForRenderCompletion(renderId);
        return new NextResponse(`renderId ${renderId}`, {
            status: 200,
            headers: {
                'Function-Name': process.env.FC_FUNCTION_NAME || ''
            }
        });
    } catch (err) {
        console.error('❌ Error while processing render task:', err);
    }
    return new NextResponse(`renderId error`, {
        status: 200,
        headers: {
            'Function-Name': process.env.FC_FUNCTION_NAME || ''
        }
    });

}

async function waitForRenderCompletion(renderId: string, timeout = 10 * 60 * 1000) {
    const interval = 3000; // 每3秒检查一次
    let elapsed = 0;

    return new Promise<void>((resolve, reject) => {
        const timer = setInterval(async () => {
            try {
                const result = await getRenderProgress(renderId);
                const status = result.status;
                console.log(`Render progress for ${renderId}: ${status}`);
                if (status === 'done') {
                    clearInterval(timer);
                    resolve();
                } else if (status === 'error') {
                    clearInterval(timer);
                    reject(new Error(`Render failed for ${renderId}`));
                }
                elapsed += interval;
                if (elapsed >= timeout) {
                    clearInterval(timer);
                    reject(new Error(`Render timeout for ${renderId}`));
                }
            } catch (e) {
                clearInterval(timer);
                reject(e);
            }
        }, interval);
    });
}


/**
 * GET endpoint
 */
export async function GET(request: NextRequest) {

    const rid = request.headers.get('x-fc-request-id') || '';

    console.log(`FC invoke Start RequestId: ${rid}`);
    // do your things
    console.log(`FC invoke End RequestId: ${rid}`);

    return new NextResponse('Hello, World!', {
        status: 200,
        headers: {
            'Function-Name': process.env.FC_FUNCTION_NAME || ''
        }
    });

}
