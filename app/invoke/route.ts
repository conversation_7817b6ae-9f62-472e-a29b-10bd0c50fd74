import {NextRequest, NextResponse} from 'next/server';
import {getRenderProgress} from "@/lib/ssr-helpers/custom-renderer";
import {RenderRequestPayloadSchema} from "@/lib/types";
import fetch from 'node-fetch'
import {z} from "zod";

async function requestWrapper<T = any>(
    requestPromise: Promise<fetch.Response>,
    responseProcessor: (response: fetch.Response) => Promise<T>
) {
    return new Promise<[T, null] | [null, Error]>(resolve => {
        requestPromise
            .then(async (response) => {
                if (response.ok) {
                    return resolve([await responseProcessor(response), null]);
                }

                const errorMessage = await response
                    .text()
                    .catch(() => 'Unknown Error(Response no data)')

                return resolve([null, new Error(errorMessage)])
            })
            .catch(e => resolve([null, e]))
    })
}

function generateError(title: string, description: any) {
    console.error(`❌ ${title}:`, description);

    return new NextResponse(`${title}: ${JSON.stringify(description)}`, {
        status: 500,
        headers: {
            'Function-Name': process.env.FC_FUNCTION_NAME || ''
        }
    });
}

async function waitForRenderCompletion(renderId: string, timeout = 10 * 60 * 1000) {
    const interval = 3000; // 每3秒检查一次
    let elapsed = 0;

    return new Promise<void>((resolve, reject) => {
        const timer = setInterval(async () => {
            // 获取渲染进度
            const [result, progressError] = await getRenderProgress(renderId)
                .then(result => [result, null]).catch(error => [null, error]);

            if (progressError) {
                clearInterval(timer);
                reject(new Error(`获取渲染进度失败: ${progressError.message}`));
                return;
            }

            const status = result.status;
            console.log(`Render progress for ${renderId}: ${status}`);

            if (status === 'done') {
                clearInterval(timer);
                resolve();
                return;
            }

            if (status === 'error') {
                clearInterval(timer);
                reject(new Error(`渲染任务执行失败: ${renderId}`));
                return;
            }

            elapsed += interval;
            if (elapsed >= timeout) {
                clearInterval(timer);
                reject(new Error(`渲染任务超时: ${renderId}`));
                return;
            }
        }, interval);
    });
}

const MqBodySchema = z.object({
    renderJson: z.any(),
    taskNo: z.string(),
    tenantId: z.any(),
    token: z.string(),
    uploadUrl: z.string(),
});

/**
 * POST endpoint
 */
export async function POST(request: NextRequest) {
    const rid = request.headers.get('x-fc-request-id') || '';
    const mqBody = await request.json();

    console.log(`FC invoke Start RequestId: ${rid}`);
    console.log('body:', mqBody)
    console.log(`FC invoke End RequestId: ${rid}`);

    if (!mqBody.renderJson) {
        console.warn('No renderJson found in message, skipping...');
        return;
    }
    if (!mqBody.taskNo) {
        console.warn('No taskNo found in message, skipping...');
        return;
    }
    if (!mqBody.tenantId) {
        console.warn('No tenantId found in message, skipping...');
        return;
    }
    if (!mqBody.uploadUrl) {
        console.warn('No uploadUrl found in message, skipping...');
        return;
    }
    if (!mqBody.token) {
        console.warn('No token found in message, skipping...');
        return;
    }

    // 步骤1：解析 JSON URL
    const [fetchUrlResult, fetchUrlError] = await requestWrapper(
        fetch(mqBody.renderJson, {
            headers: {
                Authorization: `Bearer ${mqBody.token}`,
                'tenant-id': mqBody.tenantId,
            }
        }),
        r => r.json() as Promise<{ code?: number, data: string }>
    );

    if (fetchUrlError) {
        return generateError('解析 JSON URL', fetchUrlError.message)
    }

    if (!('code' in fetchUrlResult) || fetchUrlResult.code !== 0) {
        return generateError('解析 JSON URL', fetchUrlResult)
    }

    const jsonUrl = fetchUrlResult.data;
    console.log('解析出的 JSON URL:', jsonUrl);

    // 步骤2: 下载渲染配置
    const [dataJson, downloadError] = await requestWrapper<object>(
        fetch(jsonUrl),
        r => r.json() as Promise<any>
    )

    if (downloadError) {
        return generateError('下载渲染配置', downloadError.message)
    }
    console.log('渲染配置:', JSON.stringify(dataJson, null, 2));

    // 步骤3: 校验渲染配置合法性
    const [payload, parseError] = await RenderRequestPayloadSchema
        .parseAsync({
            ...dataJson,
            ...mqBody,
            taskId: mqBody.taskNo,
        })
        .then(result => [result, null])
        .catch(error => [null, error]);

    if (parseError) {
        return generateError('请求参数不合法', parseError)
    }

    // 步骤4: 动态导入渲染模块并启动
    const [renderId, requestRenderError] = await import("@/lib/ssr-helpers/custom-renderer")
        .then(module => module.startRendering(payload))
        .then(result => [result, null])
        .catch(error => [null, error]);

    if (requestRenderError) {
        return generateError('渲染启动失败', requestRenderError.message)
    }

    // 步骤5: 等待渲染完成
    const [, renderError] = await waitForRenderCompletion(renderId)
        .then(result => [result, null])
        .catch(error => [null, error]);

    if (renderError) {
        return generateError('渲染异常', renderError.message)
    }

    // 渲染成功
    return new NextResponse(`renderId ${renderId}`, {
        status: 200,
        headers: {
            'Function-Name': process.env.FC_FUNCTION_NAME || ''
        }
    });
}

/**
 * GET endpoint
 */
export async function GET(request: NextRequest) {

    const rid = request.headers.get('x-fc-request-id') || '';

    console.log(`FC invoke Start RequestId: ${rid}`);
    // do your things
    console.log(`FC invoke End RequestId: ${rid}`);

    return new NextResponse('Hello, World!', {
        status: 200,
        headers: {
            'Function-Name': process.env.FC_FUNCTION_NAME || ''
        }
    });

}
