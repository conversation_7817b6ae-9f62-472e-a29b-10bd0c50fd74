import {RenderMediaOnProgress} from "@remotion/renderer";
import {createRemotionBundle, renderRemotionMedia, selectRemotionComposition} from "./remotion-wrapper";
import path from "path";
import fs from "fs";
import {v4 as uuidv4} from "uuid";
import {completeRender, failRender, saveRenderState, updateRenderProgress, uploadingRender,} from "./render-state";
import {sendMessage} from "@/utils/message/mq-sender";
import {uploadToOSS} from "@/utils/oss/oss-uploader";
import {getBaseUrl} from "../url-helper";
import {NextResponse} from "next/server";
import * as os from "node:os";
import {CompositionInputs, RenderRequestPayload} from "@/lib/types";
import {parseUrlFromObjectHref} from "@/utils/oss/parseUrlFromObjectHref";

// Ensure the videos directory exists
const VIDEOS_DIR = path.join(process.cwd(), "output", "rendered-videos");
if (!fs.existsSync(VIDEOS_DIR)) {
    fs.mkdirSync(VIDEOS_DIR, {recursive: true});
}

// Track rendering progress
export const renderProgress = new Map<string, number>();
export const renderStatus = new Map<string, "rendering" | "done" | "error">();
export const renderErrors = new Map<string, string>();
export const renderUrls = new Map<string, string>();
export const renderSizes = new Map<string, number>();
// 用于记录上次发送的进度
const lastSentProgress = new Map<string, number>();

async function transformUrlsForInput(
    inputProps: CompositionInputs,
    token: string,
    tenantId: string | number
): Promise<CompositionInputs> {
    const {overlays, ...rest} = inputProps

    return {
        ...rest,
        overlays: await Promise.all(
            overlays.map(async ({src, ...overlay}) => ({
                ...overlay,
                src: await parseUrlFromObjectHref(src, token, tenantId)
            }))
        )
    }
}

/**
 * Custom renderer that uses browser-based rendering to avoid platform-specific dependencies
 */
export async function startRendering(payload: RenderRequestPayload) {
    const {
        id: compositionId,
        token,
        tenantId,
        taskId,
        taskName,
        tenantName,
        uploadUrl,
        messageUrl,
        // userId,
    } = payload

    const inputProps = await transformUrlsForInput(
        payload.inputProps,
        token,
        tenantId,
    )

    console.log("inputProps:", JSON.stringify(inputProps, null, 2));

    const renderId = taskId || uuidv4();

    // Initialize render state
    saveRenderState(renderId, {
        status: "rendering",
        progress: 0,
        timestamp: Date.now(),
    });

    // Start rendering asynchronously
    void (async () => {
        try {
            // Update progress as rendering proceeds
            updateRenderProgress(renderId, 0);

            // Get the base URL for serving media files
            const baseUrl = getBaseUrl();

            // Bundle the video
            const bundleLocation = await createRemotionBundle(compositionId);

            // Select the composition
            const composition = await selectRemotionComposition(
                bundleLocation,
                compositionId,
                {
                    ...inputProps,
                    // Pass the base URL to the composition for media file access
                    baseUrl,
                }
            );

            // Get the actual duration from inputProps or use composition's duration
            const actualDurationInFrames = (inputProps.playerMetadata?.durationInFrames as number) || composition.durationInFrames;
            console.log(`${tenantName} ${taskName} Using actual duration: ${actualDurationInFrames} frames`);

            // Render the video using chromium
            await renderRemotionMedia({
                codec: "h264",
                composition: {
                    ...composition,
                    // Override the duration to use the actual duration from inputProps
                    durationInFrames: actualDurationInFrames,
                },
                serveUrl: bundleLocation,
                outputLocation: path.join(VIDEOS_DIR, `${renderId}.mp4`),
                inputProps: {
                    ...inputProps,
                    baseUrl,
                },
                // Enhanced quality settings for maximum quality output
                chromiumOptions: {
                    headless: true,
                    disableWebSecurity: false,
                    ignoreCertificateErrors: false,
                    args: [
                        "--disable-dev-shm-usage",
                        "--no-sandbox",
                        "--disable-web-security"
                    ]
                },
                timeoutInMilliseconds: 600000, // 10 minutes
                onProgress: ((progress) => {
                    // Extract just the progress percentage from the detailed progress object
                    updateRenderProgress(renderId, progress.progress);
                    const percent = Math.floor(progress.progress * 100); // 取整，避免小数点抖动
                    const lastPercent = lastSentProgress.get(renderId) ?? -1;
                    if (percent !== lastPercent) {
                        // 发送 MQ 消息
                        sendMessage(messageUrl, token, tenantId, 'render-result',
                            {
                                taskNo: renderId,
                                status: 'rendering',
                                progress: percent,
                                timestamp: Date.now(),
                            });
                        // 更新本地记录
                        lastSentProgress.set(renderId, percent);
                    }
                }) as RenderMediaOnProgress,
                // Highest quality video settings
                //crf: 1, // Lowest CRF for near-lossless quality (range 1-51, where 1 is highest quality)
                //imageFormat: "png", // Use PNG for highest quality frame captures
                //colorSpace: "bt709", // Better color accuracy
                //x264Preset: "veryslow", // Highest quality compression
                //jpegQuality: 100, // Maximum JPEG quality for any JPEG operations
                // 视频质量与性能平衡
                crf: 18,                 // 推荐视觉无损
                imageFormat: "jpeg",     // 除非需要透明通道
                jpegQuality: 95,         // 高质量JPEG
                x264Preset: "slow",      // 编码速度提升
                colorSpace: "bt709",     // 保持色彩准确
                // CPU并发控制
                concurrency: Math.max(1, Math.floor(os.cpus().length * 0.8)),
            });

            // Get file size
            const videoPath = path.join(VIDEOS_DIR, `${renderId}.mp4`);
            const stats = fs.statSync(videoPath);
            const outputPath = `/rendered-videos/${renderId}.mp4`;
            // Only attempt upload if uploadUrl is provided
            if (uploadUrl) {
                uploadingRender(renderId);
                const videoUploadResult = await uploadToOSS({
                    stsFetchUrl: uploadUrl,
                    token: token,
                    tenantId: tenantId,
                    filePath: videoPath,
                    fileName: `${renderId}.mp4`,
                    task: renderId,
                });
                console.log(`${tenantName} ${taskName} Upload ${renderId} ${videoUploadResult.url} completed successfully`);
                await sendMessage(messageUrl, token, tenantId, 'render-result', {
                    taskNo: renderId,
                    status: 'uploaded',
                    timestamp: Date.now(),
                    objectId: videoUploadResult.objectId,
                    url: videoUploadResult.url,
                    etag: videoUploadResult.etag,
                    bucket: videoUploadResult.bucket,
                    objectKey: videoUploadResult.name,
                });
            } else {
                console.log(`${tenantName} ${taskName} No upload URL provided, skipping upload`);
                await sendMessage(messageUrl, token, tenantId, 'render-result', {
                    taskNo: renderId,
                    status: 'completed_local',
                    timestamp: Date.now(),
                });
            }
            console.log(`${tenantName} ${taskName} Render ${renderId} completed successfully`);
            await sendMessage(messageUrl, token, tenantId, 'render-result', {
                taskNo: renderId,
                status: 'completed',
                timestamp: Date.now(),
            });
            completeRender(renderId, outputPath, stats.size);
        } catch (error: any) {
            await sendMessage(messageUrl, token, tenantId, 'render-result', {
                taskNo: renderId,
                status: 'failed',
                timestamp: Date.now(),
                errorMsg: error.message,
            });
            failRender(renderId, error.message);
            console.error(`${tenantName} ${taskName} Render ${renderId} failed:`, error);
        }
    })();

    return renderId;
}

/**
 * Get the current progress of a render
 */
export async function getRenderProgress(renderId: string) {
    // Add logging to debug missing renders
    console.log("Checking progress for render:", renderId);
    console.log("Available render IDs:", Array.from(renderStatus.keys()));

    // 动态导入以避免构建时的问题
    const {getRenderState} = await import("@/lib/ssr-helpers/render-state");
    const state = getRenderState(renderId);

    if (!state) {
        return NextResponse.json({
            type: "error",
            message: `No render found with renderId: ${renderId}`,
        });
    }

    const progress = state.progress || 0;
    const status = state.status || "rendering";
    const error = state.error || "Unknown error occurred";
    const url = state.url;
    const size = state.size;

    return {
        renderId,
        progress,
        status,
        error,
        url,
        size,
    };
}
