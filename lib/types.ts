// Other types remain the same
import {z} from "zod";

export const CompositionInputSchema = z.object({
    overlays: z.array(z.any()), // Replace with your actual Overlay type
    playerMetadata: z.object({
        durationInFrames: z.number(),
        width: z.number(),
        height: z.number(),
        fps: z.number(),
    }),
});

export const RenderRequestPayloadSchema = z.object({
    id: z.string(),
    tenantId: z.any(),
    token: z.string(),
    userId: z.any().optional(),
    taskId: z.any().optional(),
    taskName: z.string().optional(),
    tenantName: z.string().optional(),
    uploadUrl: z.string().optional(),
    messageUrl: z.string(),
    inputProps: CompositionInputSchema,
});

export const ProgressRequest = z.object({
    bucketName: z.string(),
    id: z.string(),
});

export type ProgressResponse =
    | { type: "error"; message: string }
    | { type: "progress"; progress: number }
    | { type: "done"; url: string; size: number };

export type CompositionInputs = z.infer<typeof CompositionInputSchema>;
export type RenderRequestPayload = z.infer<typeof RenderRequestPayloadSchema>;
