import {spawn} from 'child_process';
import * as path from 'path';
import {execFile} from 'child_process';
import {promisify} from 'util';


interface GenerateTileOptions {
    ffmpegPath?: string;          // 可执行路径，默认 'ffmpeg'
    inputVideoPath: string;       // 输入视频路径
    outputImageFile: string;      // 输出图像路径（jpg/png）
    frameWidth: number;           // 每帧宽度
    frameHeight: number;          // 每帧高度
    tileCols: number;             // 每行瓦片数
    tileRows: number;             // 每列瓦片数
    fps: number;                  // 抽帧帧率
}

export interface VideoMetadata {
    fileSize: number | null; // 字节
    width: number | null;
    height: number | null;
    duration: number | null; // 毫秒
    codecs: string | null; // 逗号分隔视频音频编码
    rotate: number | null;
    dar: string | null;
}

const execFileAsync = promisify(execFile);

interface GenerateTileImageOptions {
    renderId: string;
    inputVideoPath: string;
    outputImagePath: string;
    targetFramePixels: number; // 默认3
    tileCols?: number; // 默认 10
    duration?: number; // 默认 100毫秒一张图片
}

/**
 * 基于视频元数据自动生成瓦片图
 */
export async function generateAutoTileImage(options: GenerateTileImageOptions): Promise<string> {
    const {
        renderId,
        inputVideoPath,
        outputImagePath,
        targetFramePixels,  // 每帧图像的目标像素数，例如：230400（640x360）
        tileCols = 10,
        duration = 100,  // 毫秒
    } = options;
    const metadata = await getVideoMetadata(inputVideoPath);
    const videoWidth = metadata.width || 0;
    const videoHeight = metadata.height || 0;
    const videoDuration = metadata.duration || 1000; // 视频时长，单位：毫秒
    // 每隔 duration 毫秒截一帧
    const frameCount = Math.floor(videoDuration / duration);
    const tileRows = Math.ceil(frameCount / tileCols);
    const originalFramePixels = videoWidth * videoHeight;
    // 计算 scale，使得缩放后接近目标像素
    const scaleFactor = Math.sqrt(originalFramePixels / targetFramePixels);
    const scale = Math.max(1, Math.round(scaleFactor));
    console.log(`缩放比例：${scale}`);
    const frameWidth = Math.floor(videoWidth / scale);
    const frameHeight = Math.floor(videoHeight / scale);
    // 转换为 FPS（每秒多少帧）
    const fps = 1000 / duration;
    // 生成瓦片图
    const fileName = generateTileFilename(renderId, frameWidth, frameHeight, frameCount);
    const outputImageFile = path.join(outputImagePath, fileName);
    await generateTileImage({
        inputVideoPath,
        outputImageFile,
        frameWidth,
        frameHeight,
        tileCols,
        tileRows,
        fps,
    });
    return fileName;
}

function generateTileFilename(
    videoId: string,
    width: number,
    height: number,
    frameCount: number
): string {
    return `${videoId}_${width}x${height}_${frameCount}_tile.jpg`;
}

/**
 * 获取视频元数据（封装 ffprobe）
 * @param filePath 视频文件路径
 */
export async function getVideoMetadata(filePath: string): Promise<VideoMetadata> {
    const ffprobePath = 'ffprobe';
    const args = [
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_format',
        '-show_streams',
        filePath,
    ];

    try {
        const {stdout} = await execFileAsync(ffprobePath, args);
        const data = JSON.parse(stdout);
        console.log('Video metadata:', data)

        const format = data.format || {};
        const streams = data.streams || [];

        // 获取视频流（第一个 video 类型流）
        const videoStream = streams.find((s: any) => s.codec_type === 'video') || {};
        // 获取音频流（第一个 audio 类型流）
        const audioStream = streams.find((s: any) => s.codec_type === 'audio') || {};

        // 旋转角度，有些视频流在tags里定义了rotate
        let rotate: number | null = null;
        if (videoStream.tags && videoStream.tags.rotate) {
            rotate = parseInt(videoStream.tags.rotate, 10);
        } else if (videoStream.side_data_list) {
            // ffprobe 较新版本可能放在 side_data_list
            const rotateInfo = videoStream.side_data_list.find((item: any) => item.rotation !== undefined);
            if (rotateInfo) rotate = rotateInfo.rotation;
        }
        // 显示宽高比
        const dar = videoStream.display_aspect_ratio || null;
        // 编解码器字符串，视频编码 + 音频编码，用逗号分隔
        const codecs = [videoStream.codec_name, audioStream.codec_name].filter(Boolean).join(',');
        // 文件大小，格式里的 size 字段通常是字符串，需要转数字
        const fileSize = format.size ? parseInt(format.size, 10) : null;
        // 时长 秒 -> 毫秒
        const duration = format.duration ? Math.round(parseFloat(format.duration) * 1000) : null;

        return {
            fileSize,
            width: videoStream.width || null,
            height: videoStream.height || null,
            duration,
            codecs: codecs || null,
            rotate,
            dar,
        };
    } catch (error) {
        console.error('ffprobe error:', error);
        throw new Error('获取视频元数据失败');
    }
}


export async function generateTileImage(options: GenerateTileOptions): Promise<void> {
    const {
        ffmpegPath = 'ffmpeg',
        inputVideoPath,
        outputImageFile,
        frameWidth,
        frameHeight,
        tileCols,
        tileRows,
        fps,
    } = options;

    const vfFilter = `fps=${fps.toFixed(3)},scale=${frameWidth}:${frameHeight},tile=${tileCols}x${tileRows}`;

    return new Promise((resolve, reject) => {
        const args = [
            '-i', inputVideoPath,
            '-vf', vfFilter,
            '-frames:v', '1',
            '-update', '1',
            outputImageFile
        ];

        const ffmpeg = spawn(ffmpegPath, args);

        ffmpeg.stdout.on('data', (data) => {
            console.log(`[ffmpeg stdout] ${data}`);
        });

        ffmpeg.stderr.on('data', (data) => {
            console.log(`[ffmpeg stderr] ${data}`);
        });

        ffmpeg.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`FFmpeg process exited with code ${code}`));
            }
        });

        ffmpeg.on('error', (err) => {
            reject(err);
        });
    });
}
