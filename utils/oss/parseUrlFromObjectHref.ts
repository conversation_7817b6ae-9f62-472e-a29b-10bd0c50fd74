import axios from "axios";
import {<PERSON><PERSON><PERSON>} from "jsdom";

export async function parseUrlFromObjectHref(objectHref: string, token: string, tenantId: string | number): Promise<string> {
  if (!/\/oss\/object-href\/\w+/.test(objectHref)) {
    return objectHref
  }

  const [fetchResult, fetchError] = await axios
    .get(objectHref, {
      headers: {
        Authorization: `Bearer ${token}`,
        'tenant-id': tenantId,
      }
    })
    .then(r => [r.data, null])
    .catch(error => [null, new Error(`Cannot fetch object href(${objectHref}): ${error.message}`)])

  if (fetchError) throw fetchError

  const [dom, parseError] = await new Promise<[JSDOM, null] | [null, Error]>(
    (resolve, reject) => {
      try {
        return resolve([
          new JSDOM(fetchResult),
          null
        ])
      } catch {
        return reject([
          null,
          new Error(`Failed to parse DOM from response data "${fetchResult}"`)
        ])
      }
    }
  )

  if (parseError) throw parseError

  const aTag = dom.window.document.querySelector('a')
  if (!aTag) throw new Error('No valid <a> tag in parsed dom')

  const href = aTag.href
  if (!href) throw new Error('No content in `href` of parsed <a> tag')

  console.debug(`parse object href ${objectHref} ====> ${href}`)
  return href
}
