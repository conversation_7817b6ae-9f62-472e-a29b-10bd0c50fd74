// mq-sender.ts

import axios from "axios";

export async function sendMessage(messageUrl: string,
                                  token: string,
                                  tenantId: string,
                                  topic: string,
                                  messageBody: Record<string, any>
) {
    const topicMessageUrl = `${messageUrl}/${topic}`;
    try {
        console.log(`[HTTP] Message sent to ${topic}`, messageBody);
        const response = await axios.post(
            topicMessageUrl,
            messageBody,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'tenant-id': tenantId,
                    "Content-Type": "application/json",
                },
                timeout: 10000, // 10 秒超时
            }
        );
        console.log('response', response.data)
        return response.data;
    } catch (error) {
        console.error(`[HTTP] Failed to send message to ${topic}:`, error);
    }
}