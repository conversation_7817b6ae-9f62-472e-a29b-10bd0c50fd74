var Dt = Object.defineProperty;
var zt = (t, e, n) => e in t ? Dt(t, e, { enumerable: !0, configurable: !0, writable: !0, value: n }) : t[e] = n;
var se = (t, e, n) => zt(t, typeof e != "symbol" ? e + "" : e, n);
import et, { useContext as Vt, memo as jt, useCallback as Ne, useState as rt, useEffect as Le, useMemo as Ye, useRef as It, Fragment as Bt } from "react";
import { useCurrentFrame as Re, interpolate as x, Easing as Ee, Audio as Ut, delayRender as $t, continueRender as $e, OffthreadVideo as Yt, Sequence as at, AbsoluteFill as Nt, registerRoot as Ht, Composition as Xt } from "remotion";
import * as qt from "opentype.js";
var Ae = { exports: {} }, ve = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var it;
function Gt() {
  if (it) return ve;
  it = 1;
  var t = Symbol.for("react.transitional.element"), e = Symbol.for("react.fragment");
  function n(s, r, a) {
    var i = null;
    if (a !== void 0 && (i = "" + a), r.key !== void 0 && (i = "" + r.key), "key" in r) {
      a = {};
      for (var o in r)
        o !== "key" && (a[o] = r[o]);
    } else a = r;
    return r = a.ref, {
      $$typeof: t,
      type: s,
      key: i,
      ref: r !== void 0 ? r : null,
      props: a
    };
  }
  return ve.Fragment = e, ve.jsx = n, ve.jsxs = n, ve;
}
var be = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ot;
function Jt() {
  return ot || (ot = 1, process.env.NODE_ENV !== "production" && function() {
    function t(l) {
      if (l == null) return null;
      if (typeof l == "function")
        return l.$$typeof === G ? null : l.displayName || l.name || null;
      if (typeof l == "string") return l;
      switch (l) {
        case N:
          return "Fragment";
        case O:
          return "Profiler";
        case j:
          return "StrictMode";
        case W:
          return "Suspense";
        case F:
          return "SuspenseList";
        case q:
          return "Activity";
      }
      if (typeof l == "object")
        switch (typeof l.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), l.$$typeof) {
          case C:
            return "Portal";
          case E:
            return (l.displayName || "Context") + ".Provider";
          case I:
            return (l._context.displayName || "Context") + ".Consumer";
          case P:
            var w = l.render;
            return l = l.displayName, l || (l = w.displayName || w.name || "", l = l !== "" ? "ForwardRef(" + l + ")" : "ForwardRef"), l;
          case L:
            return w = l.displayName || null, w !== null ? w : t(l.type) || "Memo";
          case B:
            w = l._payload, l = l._init;
            try {
              return t(l(w));
            } catch {
            }
        }
      return null;
    }
    function e(l) {
      return "" + l;
    }
    function n(l) {
      try {
        e(l);
        var w = !1;
      } catch {
        w = !0;
      }
      if (w) {
        w = console;
        var A = w.error, Z = typeof Symbol == "function" && Symbol.toStringTag && l[Symbol.toStringTag] || l.constructor.name || "Object";
        return A.call(
          w,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          Z
        ), e(l);
      }
    }
    function s(l) {
      if (l === N) return "<>";
      if (typeof l == "object" && l !== null && l.$$typeof === B)
        return "<...>";
      try {
        var w = t(l);
        return w ? "<" + w + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function r() {
      var l = ye.A;
      return l === null ? null : l.getOwner();
    }
    function a() {
      return Error("react-stack-top-frame");
    }
    function i(l) {
      if (oe.call(l, "key")) {
        var w = Object.getOwnPropertyDescriptor(l, "key").get;
        if (w && w.isReactWarning) return !1;
      }
      return l.key !== void 0;
    }
    function o(l, w) {
      function A() {
        U || (U = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          w
        ));
      }
      A.isReactWarning = !0, Object.defineProperty(l, "key", {
        get: A,
        configurable: !0
      });
    }
    function c() {
      var l = t(this.type);
      return xe[l] || (xe[l] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), l = this.props.ref, l !== void 0 ? l : null;
    }
    function d(l, w, A, Z, J, Y, ze, Ve) {
      return A = Y.ref, l = {
        $$typeof: y,
        type: l,
        key: w,
        props: Y,
        _owner: J
      }, (A !== void 0 ? A : null) !== null ? Object.defineProperty(l, "ref", {
        enumerable: !1,
        get: c
      }) : Object.defineProperty(l, "ref", { enumerable: !1, value: null }), l._store = {}, Object.defineProperty(l._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(l, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(l, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: ze
      }), Object.defineProperty(l, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: Ve
      }), Object.freeze && (Object.freeze(l.props), Object.freeze(l)), l;
    }
    function h(l, w, A, Z, J, Y, ze, Ve) {
      var M = w.children;
      if (M !== void 0)
        if (Z)
          if (V(M)) {
            for (Z = 0; Z < M.length; Z++)
              v(M[Z]);
            Object.freeze && Object.freeze(M);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else v(M);
      if (oe.call(w, "key")) {
        M = t(l);
        var de = Object.keys(w).filter(function(Wt) {
          return Wt !== "key";
        });
        Z = 0 < de.length ? "{key: someKey, " + de.join(": ..., ") + ": ...}" : "{key: someKey}", _e[M + Z] || (de = 0 < de.length ? "{" + de.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          Z,
          M,
          de,
          M
        ), _e[M + Z] = !0);
      }
      if (M = null, A !== void 0 && (n(A), M = "" + A), i(w) && (n(w.key), M = "" + w.key), "key" in w) {
        A = {};
        for (var Be in w)
          Be !== "key" && (A[Be] = w[Be]);
      } else A = w;
      return M && o(
        A,
        typeof l == "function" ? l.displayName || l.name || "Unknown" : l
      ), d(
        l,
        M,
        Y,
        J,
        r(),
        A,
        ze,
        Ve
      );
    }
    function v(l) {
      typeof l == "object" && l !== null && l.$$typeof === y && l._store && (l._store.validated = 1);
    }
    var g = et, y = Symbol.for("react.transitional.element"), C = Symbol.for("react.portal"), N = Symbol.for("react.fragment"), j = Symbol.for("react.strict_mode"), O = Symbol.for("react.profiler"), I = Symbol.for("react.consumer"), E = Symbol.for("react.context"), P = Symbol.for("react.forward_ref"), W = Symbol.for("react.suspense"), F = Symbol.for("react.suspense_list"), L = Symbol.for("react.memo"), B = Symbol.for("react.lazy"), q = Symbol.for("react.activity"), G = Symbol.for("react.client.reference"), ye = g.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, oe = Object.prototype.hasOwnProperty, V = Array.isArray, ce = console.createTask ? console.createTask : function() {
      return null;
    };
    g = {
      "react-stack-bottom-frame": function(l) {
        return l();
      }
    };
    var U, xe = {}, Ce = g["react-stack-bottom-frame"].bind(
      g,
      a
    )(), Oe = ce(s(a)), _e = {};
    be.Fragment = N, be.jsx = function(l, w, A, Z, J) {
      var Y = 1e4 > ye.recentlyCreatedOwnerStacks++;
      return h(
        l,
        w,
        A,
        !1,
        Z,
        J,
        Y ? Error("react-stack-top-frame") : Ce,
        Y ? ce(s(l)) : Oe
      );
    }, be.jsxs = function(l, w, A, Z, J) {
      var Y = 1e4 > ye.recentlyCreatedOwnerStacks++;
      return h(
        l,
        w,
        A,
        !0,
        Z,
        J,
        Y ? Error("react-stack-top-frame") : Ce,
        Y ? ce(s(l)) : Oe
      );
    };
  }()), be;
}
var ct;
function Qt() {
  return ct || (ct = 1, process.env.NODE_ENV === "production" ? Ae.exports = Gt() : Ae.exports = Jt()), Ae.exports;
}
var _ = Qt();
const Lt = et.createContext(null), Te = () => Vt(Lt), dt = {
  fontFamily: "Inter, sans-serif",
  fontSize: "2.5rem",
  lineHeight: 1.4,
  textAlign: "center",
  color: "#FFFFFF",
  textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
  padding: "24px",
  highlightStyle: {
    backgroundColor: "rgba(20, 184, 166, 0.95)",
    scale: 1.1,
    fontWeight: 600,
    textShadow: "2px 2px 4px rgba(0,0,0,0.3)"
  }
}, Kt = ({
  overlay: t
}) => {
  const { playerMetadata: { fps: e } } = Te(), s = Re() / e * 1e3, r = t.styles || dt, a = t.captions.find(
    (o) => s >= o.startMs && s <= o.endMs
  );
  if (!a) return null;
  const i = (o) => {
    var c;
    return (c = o == null ? void 0 : o.words) == null ? void 0 : c.map((d, h) => {
      const v = s >= d.startMs && s <= d.endMs, g = v ? Math.min((s - d.startMs) / 300, 1) : 0, y = r.highlightStyle || dt.highlightStyle;
      return /* @__PURE__ */ _.jsx(
        "span",
        {
          className: "inline-block transition-all duration-200",
          style: {
            color: v ? y == null ? void 0 : y.color : r.color,
            backgroundColor: v ? y == null ? void 0 : y.backgroundColor : "transparent",
            opacity: v ? 1 : 0.85,
            transform: v ? `scale(${1 + (y != null && y.scale ? (y.scale - 1) * g : 0.08)})` : "scale(1)",
            fontWeight: v ? (y == null ? void 0 : y.fontWeight) || 600 : r.fontWeight || 400,
            textShadow: v ? y == null ? void 0 : y.textShadow : r.textShadow,
            padding: (y == null ? void 0 : y.padding) || "4px 8px",
            borderRadius: (y == null ? void 0 : y.borderRadius) || "4px",
            margin: "0 2px"
          },
          children: d.word
        },
        `${d.word}-${h}`
      );
    });
  };
  return /* @__PURE__ */ _.jsx(
    "div",
    {
      className: "absolute inset-0 flex items-center justify-center p-4",
      style: {
        ...r,
        width: "100%",
        height: "100%"
      },
      children: /* @__PURE__ */ _.jsx(
        "div",
        {
          className: "leading-relaxed tracking-wide",
          style: {
            whiteSpace: "pre-wrap",
            width: "100%",
            textAlign: "center",
            wordBreak: "break-word",
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "center",
            alignItems: "center",
            gap: "2px"
          },
          children: i(a)
        }
      )
    }
  );
}, ue = {
  fade: {
    name: "Fade",
    preview: "Simple fade in/out",
    enter: (t) => ({
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  slideRight: {
    name: "Slide",
    preview: "Slide in from left",
    isPro: !0,
    enter: (t) => ({
      transform: `translateX(${x(t, [0, 15], [-100, 0], {
        extrapolateRight: "clamp"
      })}%)`,
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateX(${x(
        t,
        [e - 15, e],
        [0, 100],
        { extrapolateLeft: "clamp" }
      )}%)`,
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  scale: {
    name: "Scale",
    preview: "Scale in/out",
    enter: (t) => ({
      transform: `scale(${x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `scale(${x(
        t,
        [e - 15, e],
        [1, 0],
        { extrapolateLeft: "clamp" }
      )})`,
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  bounce: {
    name: "Bounce",
    preview: "Elastic bounce entrance",
    isPro: !0,
    enter: (t) => ({
      transform: `translateY(${x(
        t,
        [0, 10, 13, 15],
        [100, -10, 5, 0],
        { extrapolateRight: "clamp" }
      )}px)`,
      opacity: x(t, [0, 10], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateY(${x(
        t,
        [e - 15, e - 13, e - 10, e],
        [0, 5, -10, 100],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: x(t, [e - 10, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  flipX: {
    name: "Flip",
    preview: "3D flip around X axis",
    isPro: !0,
    enter: (t) => ({
      transform: `perspective(400px) rotateX(${x(
        t,
        [0, 15],
        [90, 0],
        { extrapolateRight: "clamp" }
      )}deg)`,
      opacity: x(t, [0, 5, 15], [0, 0.7, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `perspective(400px) rotateX(${x(
        t,
        [e - 15, e],
        [0, -90],
        { extrapolateLeft: "clamp" }
      )}deg)`,
      opacity: x(
        t,
        [e - 15, e - 5, e],
        [1, 0.7, 0],
        {
          extrapolateLeft: "clamp"
        }
      )
    })
  },
  zoomBlur: {
    name: "Zoom",
    preview: "Zoom with blur effect",
    isPro: !0,
    enter: (t) => ({
      transform: `scale(${x(t, [0, 15], [1.5, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      }),
      filter: `blur(${x(t, [0, 15], [10, 0], {
        extrapolateRight: "clamp"
      })}px)`
    }),
    exit: (t, e) => ({
      transform: `scale(${x(
        t,
        [e - 15, e],
        [1, 1.5],
        { extrapolateLeft: "clamp" }
      )})`,
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      }),
      filter: `blur(${x(t, [e - 15, e], [0, 10], {
        extrapolateLeft: "clamp"
      })}px)`
    })
  },
  slideUp: {
    name: "Slide",
    preview: "Modern slide from bottom",
    enter: (t) => ({
      transform: `translateY(${x(t, [0, 15], [30, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translateY(${x(
        t,
        [e - 15, e],
        [0, -30],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  snapRotate: {
    name: "Snap",
    preview: "Quick rotate with snap",
    isPro: !0,
    enter: (t) => ({
      transform: `rotate(${x(t, [0, 8, 12, 15], [-10, 5, -2, 0], {
        extrapolateRight: "clamp"
      })}deg) scale(${x(t, [0, 15], [0.8, 1], {
        extrapolateRight: "clamp"
      })})`,
      opacity: x(t, [0, 10], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `rotate(${x(
        t,
        [e - 15, e - 12, e - 8, e],
        [0, -2, 5, -10],
        { extrapolateLeft: "clamp" }
      )}deg) scale(${x(t, [e - 15, e], [1, 0.8], {
        extrapolateLeft: "clamp"
      })})`,
      opacity: x(t, [e - 10, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  },
  glitch: {
    name: "Glitch",
    preview: "Digital glitch effect",
    isPro: !0,
    enter: (t) => {
      const e = x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      }), n = t % 3 === 0 ? (Math.random() * 10 - 5) * (1 - e) : 0, s = t % 4 === 0 ? (Math.random() * 8 - 4) * (1 - e) : 0;
      return {
        transform: `translate(${n}px, ${s}px) scale(${x(
          t,
          [0, 3, 6, 10, 15],
          [0.9, 1.05, 0.95, 1.02, 1],
          { extrapolateRight: "clamp" }
        )})`,
        opacity: x(t, [0, 3, 5, 15], [0, 0.7, 0.8, 1], {
          extrapolateRight: "clamp"
        })
      };
    },
    exit: (t, e) => {
      const n = x(t, [e - 15, e], [0, 1], {
        extrapolateLeft: "clamp"
      }), s = (e - t) % 3 === 0 ? (Math.random() * 10 - 5) * n : 0, r = (e - t) % 4 === 0 ? (Math.random() * 8 - 4) * n : 0;
      return {
        transform: `translate(${s}px, ${r}px) scale(${x(
          t,
          [e - 15, e - 10, e - 6, e - 3, e],
          [1, 1.02, 0.95, 1.05, 0.9],
          { extrapolateLeft: "clamp" }
        )})`,
        opacity: x(
          t,
          [e - 15, e - 5, e - 3, e],
          [1, 0.8, 0.7, 0],
          {
            extrapolateLeft: "clamp"
          }
        )
      };
    }
  },
  swipeReveal: {
    name: "Swipe",
    preview: "Reveals content with a swipe",
    isPro: !0,
    enter: (t) => ({
      transform: `translateX(${x(t, [0, 15], [0, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: 1,
      clipPath: `inset(0 ${x(t, [0, 15], [100, 0], {
        extrapolateRight: "clamp"
      })}% 0 0)`
    }),
    exit: (t, e) => ({
      transform: `translateX(${x(
        t,
        [e - 15, e],
        [0, 0],
        { extrapolateLeft: "clamp" }
      )}px)`,
      opacity: 1,
      clipPath: `inset(0 0 0 ${x(
        t,
        [e - 15, e],
        [0, 100],
        { extrapolateLeft: "clamp" }
      )}%)`
    })
  },
  floatIn: {
    name: "Float",
    preview: "Smooth floating entrance",
    enter: (t) => ({
      transform: `translate(${x(t, [0, 15], [10, 0], {
        extrapolateRight: "clamp"
      })}px, ${x(t, [0, 15], [-20, 0], {
        extrapolateRight: "clamp"
      })}px)`,
      opacity: x(t, [0, 15], [0, 1], {
        extrapolateRight: "clamp"
      })
    }),
    exit: (t, e) => ({
      transform: `translate(${x(
        t,
        [e - 15, e],
        [0, -10],
        { extrapolateLeft: "clamp" }
      )}px, ${x(t, [e - 15, e], [0, -20], {
        extrapolateLeft: "clamp"
      })}px)`,
      opacity: x(t, [e - 15, e], [1, 0], {
        extrapolateLeft: "clamp"
      })
    })
  }
};
function lt() {
  return process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";
}
function Pt(t) {
  return t.startsWith("http://") || t.startsWith("https://") ? t : t.startsWith("/") ? `${lt()}${t}` : `${lt()}/${t}`;
}
const en = ({
  overlay: t,
  baseUrl: e
}) => {
  var v;
  const n = Re(), { playerMetadata: { fps: s } } = Te();
  let r = t.src;
  t.src.startsWith("/") && e ? r = `${e}${t.src}` : t.src.startsWith("/") && (r = Pt(t.src));
  let a = ((v = t.styles) == null ? void 0 : v.volume) ?? 1;
  const i = t.fadeInDuration ?? 0, o = t.fadeOutDuration ?? 0, c = i * s, d = o * s, h = t.durationInFrames;
  if (c > 0 && n < c) {
    const g = x(
      n,
      [0, c],
      [0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
        easing: Ee.out(Ee.cubic)
      }
    );
    a *= g;
  }
  if (d > 0 && n > h - d) {
    const g = x(
      n,
      [h - d, h],
      [1, 0],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
        easing: Ee.out(Ee.cubic)
      }
    );
    a *= g;
  }
  return /* @__PURE__ */ _.jsx(
    Ut,
    {
      src: r,
      startFrom: t.startFromSound || 0,
      volume: a,
      playbackRate: t.speed ?? 1
    }
  );
}, tn = jt(() => /* @__PURE__ */ _.jsx(
  "div",
  {
    style: {
      width: "100%",
      height: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(0, 0, 0, 0.1)",
      borderRadius: "4px"
    },
    children: "加载中..."
  }
));
tn.displayName = "LoadingPlaceholder";
const nn = jt(() => /* @__PURE__ */ _.jsx(
  "div",
  {
    style: {
      width: "100%",
      height: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(255, 0, 0, 0.1)",
      borderRadius: "4px"
    },
    children: "贴纸未找到"
  }
));
nn.displayName = "NotFoundPlaceholder";
const sn = ({ overlay: t }) => {
  var c, d, h, v, g;
  const { playerMetadata: { fps: e } } = Te(), n = Re(), s = n >= t.durationInFrames - e, r = !s && ((c = t.styles.animation) != null && c.enter) ? (d = ue[t.styles.animation.enter]) == null ? void 0 : d.enter(
    n,
    t.durationInFrames
  ) : {}, a = s && ((h = t.styles.animation) != null && h.exit) ? (v = ue[t.styles.animation.exit]) == null ? void 0 : v.exit(
    n,
    t.durationInFrames
  ) : {}, i = {
    width: "100%",
    height: "100%",
    objectPosition: t.styles.objectPosition,
    opacity: t.styles.opacity,
    transform: t.styles.transform || "none",
    filter: t.styles.filter || "none",
    borderRadius: t.styles.borderRadius || "0px",
    boxShadow: t.styles.boxShadow || "none",
    border: t.styles.border || "none",
    ...s ? a : r
  }, o = {
    width: "100%",
    height: "100%",
    padding: t.styles.padding || "0px",
    backgroundColor: t.styles.paddingBackgroundColor || "transparent",
    display: "flex",
    // Use flexbox for centering
    alignItems: "center",
    justifyContent: "center"
  };
  return /* @__PURE__ */ _.jsx(
    "div",
    {
      style: {
        width: "100%",
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        borderRadius: "4px",
        opacity: ((g = t.styles) == null ? void 0 : g.opacity) || 1,
        transform: `rotate(${t.rotation || 0}deg)`,
        ...o
      },
      children: /* @__PURE__ */ _.jsx(
        "img",
        {
          src: t.localSrc || t.src,
          style: {
            width: "100%",
            height: "100%",
            objectFit: "contain",
            ...i
          },
          alt: "贴纸"
        }
      )
    }
  );
};
function rn(t) {
  const e = t.match(/left|center|right/gi) || [], n = e.length === 0 ? "left" : e[0], s = t.match(/baseline|top|bottom|middle/gi) || [], r = s.length === 0 ? "baseline" : s[0];
  return { horizontal: n, vertical: r };
}
function an(t, e) {
  return t.charToGlyph(e).name !== ".notdef";
}
class on {
  constructor(e, n) {
    this.font = e, this.fallbackFont = n;
  }
  toVector(e, n = {}) {
    const s = Object.keys(n.attributes || {}).map((c) => `${c}="${n.attributes[c]}"`).join(" "), { d: r, height: a, cursorX: i } = this.getD(e, n);
    return {
      path: s ? `<path ${s} d="${r}"/>` : `<path d="${r}"/>`,
      width: i,
      height: a
    };
  }
  _getHeight(e) {
    const n = 1 / this.font.unitsPerEm * e;
    return (this.font.ascender - this.font.descender) * n;
  }
  _getWidth(e, n) {
    return this.font.getAdvanceWidth(e, n);
  }
  getD(e, n = {}) {
    const s = n.fontSize || 72, r = "kerning" in n ? n.kerning : !0, a = n.letterSpacing || 0, i = n.tracking || 0;
    let o = n.x || 0;
    const c = n.y || 0, d = rn(n.anchor || ""), h = this._getHeight(s), v = this.font.ascender * (s / this.font.unitsPerEm);
    let g = c;
    switch (d.vertical) {
      case "baseline":
        g = c;
        break;
      case "top":
        g = c + v;
        break;
      case "middle":
        g = c + v - h / 2;
        break;
      case "bottom":
        g = c + v - h;
        break;
    }
    const y = [];
    let C = null, N = null;
    for (let j = 0; j < e.length; j++) {
      const O = e[j], I = an(this.font, O) ? this.font : this.fallbackFont, E = 1 / I.unitsPerEm * s, P = I.charToGlyph(O);
      if (r && C && N === I) {
        const F = I.getKerningValue(C, P);
        o += F * E;
      }
      const W = I.getPath(O, o, g, s, {
        kerning: !1,
        tracking: i
      });
      if (y.push(W), P.advanceWidth) {
        const F = P.advanceWidth * E;
        o += F;
      }
      a ? o += a * s : i && (o += i / 1e3 * s), C = P, N = I;
    }
    return {
      d: y.map((j) => j.toPathData(4)).join(" "),
      height: h,
      cursorX: o
    };
  }
}
const ae = class ae {
  constructor() {
    se(this, "cache", /* @__PURE__ */ new Map());
  }
  static getInstance() {
    return ae.instance || (ae.instance = new ae()), ae.instance;
  }
  getCacheKey(e, n, s, r) {
    return r !== void 0 ? `${e}:${n}:${s}:${r}` : `${e}:${n}:${s}`;
  }
  getCachedWidth(e, n, s, r) {
    const a = this.getCacheKey(e, n, s, r);
    return this.cache.get(a) || null;
  }
  setCachedWidth(e, n, s, r, a) {
    const i = this.getCacheKey(e, n, s, a);
    this.cache.set(i, r);
  }
};
se(ae, "instance", null);
let He = ae;
class cn {
  constructor(e, n, s, r = 0) {
    se(this, "fontPath");
    se(this, "fontSize");
    se(this, "containerWidth");
    se(this, "letterSpacing");
    this.fontPath = e, this.fontSize = n, this.containerWidth = s, this.letterSpacing = r;
  }
  /**
   * 获取文本片段的宽度（包含字间距）
   */
  getTextWidth(e) {
    if (!e) return 0;
    try {
      const n = this.fontPath.getAdvanceWidth(e, this.fontSize), s = Math.max(0, e.length - 1) * this.letterSpacing;
      return n + s;
    } catch (n) {
      console.warn("[贪心换行] 获取文本宽度失败:", e, n);
      const s = e.length * this.fontSize * 0.6, r = Math.max(0, e.length - 1) * this.letterSpacing;
      return s + r;
    }
  }
  /**
   * 贪心算法：考虑单词边界
   * @param content 要分割的文本内容
   * @param respectWordBoundary 是否尊重单词边界（对中文无效）
   * @returns 分割后的字符串数组
   */
  wrapText(e, n = !1) {
    if (!e || !this.fontPath)
      return [e || ""];
    const s = [], r = e.split(`
`);
    for (const a of r) {
      if (!a.trim()) {
        s.push("");
        continue;
      }
      n && /^[a-zA-Z\s]+$/.test(a) ? this.wrapEnglishText(a, s) : this.wrapChineseText(a, s);
    }
    return s.length > 0 ? s : [""];
  }
  /**
   * 处理英文文本换行（按单词）
   */
  wrapEnglishText(e, n) {
    const s = e.split(/(\s+)/);
    let r = "", a = 0;
    for (const i of s) {
      const o = this.getTextWidth(i);
      a + o > this.containerWidth && r.trim().length > 0 ? (n.push(r.trimEnd()), r = i, a = o) : (r += i, a += o);
    }
    r.trim().length > 0 && n.push(r.trimEnd());
  }
  /**
   * 处理中文文本换行（按字符）
   */
  wrapChineseText(e, n) {
    let s = "", r = 0;
    for (let a = 0; a < e.length; a++) {
      const i = e[a], o = this.fontPath.getAdvanceWidth(i, this.fontSize), c = s.length === 0 ? o : o + this.letterSpacing;
      r + c > this.containerWidth && s.length > 0 ? (n.push(s), s = i, r = o) : (s += i, r += c);
    }
    s.length > 0 && n.push(s);
  }
  /**
   * 获取换行后的总高度
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getWrappedTextHeight(lines: string[], lineHeight?: number): number {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //   return lines.length * actualLineHeight
  // }
  /**
   * 获取每行的详细信息
   * @param lines 文本行数组
   * @param lineHeight 自定义行高，如果不提供则使用默认值
   */
  // public getLineDetails(lines: string[], lineHeight?: number): Array<{ line: string, width: number, height: number }> {
  //   const actualLineHeight = lineHeight || this.fontSize * 1.2
  //
  //   return lines.map(line => ({
  //     line,
  //     width: this.getTextWidth(line),
  //     height: actualLineHeight
  //   }))
  // }
  /**
   * 获取换行后的总高度（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  getWrappedTextHeightWithSpacing(e, n, s = 0) {
    const r = n || this.fontSize * 1.2, a = e.length, i = s * this.fontSize;
    return a * r + Math.max(0, a - 1) * i;
  }
  /**
   * 获取每行的详细信息（包含行间距）
   * @param lines 文本行数组
   * @param lineHeight 行高，如果不提供则使用默认值
   * @param lineSpacingRatio 行间距倍数，默认为0
   */
  getLineDetailsWithSpacing(e, n, s = 0) {
    const r = n || this.fontSize * 1.2, a = s * this.fontSize;
    return e.map((i, o) => ({
      line: i,
      width: this.getTextWidth(i),
      height: r,
      y: o * (r + a)
      // 每行的Y坐标包含行间距
    }));
  }
}
const ut = He.getInstance(), dn = 1, ht = (t, e, n, s, r = 0) => {
  if (!n)
    return 1;
  const a = ut.getCachedWidth(e, n, s, r);
  if (a !== null)
    return a;
  try {
    const i = t.getAdvanceWidth(n, s), o = Math.max(0, n.length - 1) * r, c = i + o;
    return ut.setCachedWidth(e, n, s, c, r), c;
  } catch (i) {
    return console.error("[增强文本渲染器] 计算文本宽度失败:", i), 1;
  }
};
function ln(t, e, n, s, r, a = 0, i = !1) {
  try {
    return new cn(t, r, s, a).wrapText(n, i).map((d) => ({
      content: d,
      width: ht(t, e, d, r, a)
    }));
  } catch (o) {
    return console.error("[增强文本渲染器] 优化贪心换行失败:", o), [{
      content: n,
      width: ht(t, e, n, r, a)
    }];
  }
}
function un(t, e, n) {
  const {
    width: s = e.width,
    content: r = e.content,
    fontSize: a = e.styles.fontSize ?? 0,
    lineSpacing: i = e.styles.lineSpacing ?? 0,
    letterSpacing: o = e.styles.letterSpacing ?? 0
  } = n || {}, c = a * dn, d = i * a, h = ln(
    t,
    e.src,
    r,
    s,
    a,
    o,
    !0
  ), v = h.length, g = v * c + Math.max(0, v - 1) * d, y = Math.max(...h.map((C) => C.width));
  return {
    wrappedLines: h,
    lineHeight: c,
    totalHeight: g,
    maxLineWidth: y
  };
}
const hn = (t, e) => ({
  buildCalcTextRenderInfoFunction: Ne(
    () => !e || !t ? null : (s) => {
      const {
        width: r = e.width,
        fontSize: a = e.styles.fontSize,
        lineSpacing: i = e.styles.lineSpacing ?? 0,
        letterSpacing: o = e.styles.letterSpacing ?? 0
      } = s || {}, c = r / 2, d = e.height / 2, h = e.styles.textAlign, v = i * a, { totalHeight: g, lineHeight: y, wrappedLines: C, maxLineWidth: N } = un(
        t,
        e,
        s
      ), j = () => {
        switch (h) {
          case "left":
            return 0;
          case "right":
            return r;
          case "center":
          default:
            return c;
        }
      }, O = 1, I = j(), E = d - g / 2 + y / 2;
      return {
        x: I,
        y: E,
        scale: O,
        fontSize: a,
        letterSpacing: o,
        wrappedLines: C,
        lineHeight: y,
        totalHeight: g,
        minHeight: g,
        minWidth: N,
        lineSpacing: v
      };
    },
    [e, t]
  )
}), tt = 0, fn = ({ overlay: t, font: e, renderInfo: n }) => {
  const { styles: s } = t, r = !!(s.strokeEnabled && t.styles.strokeWidth && t.styles.strokeColor), a = !!(s.shadowEnabled && t.styles.shadowDistance && t.styles.shadowColor);
  if (!r && !a)
    return null;
  const i = It(null);
  if (t.width === 0)
    return null;
  const o = Ne(
    () => {
      const g = [];
      if (a) {
        const y = t.styles.shadowDistance ? t.styles.shadowDistance / 2 : 0, C = t.styles.shadowAngle || 45, N = t.styles.shadowBlur || 2, j = t.styles.shadowColor || "#000000", O = t.styles.shadowOpacity || 0.5;
        if (y > 0) {
          const I = C * Math.PI / 180, E = Math.cos(I) * y, P = Math.sin(I) * y;
          g.push(
            /* @__PURE__ */ _.jsx(
              "filter",
              {
                id: `shadow-${t.id}`,
                x: "-100%",
                y: "-100%",
                width: "300%",
                height: "300%",
                filterUnits: "userSpaceOnUse",
                colorInterpolationFilters: "sRGB",
                children: /* @__PURE__ */ _.jsx(
                  "feDropShadow",
                  {
                    dx: E,
                    dy: P,
                    stdDeviation: N,
                    floodColor: j,
                    floodOpacity: O
                  }
                )
              },
              "shadow"
            )
          );
        }
      }
      return g;
    },
    [a, t.styles, t.id]
  ), c = Ne(
    () => {
      try {
        const g = t.styles.strokeWidth ?? 0, y = t.styles.strokeColor || "#000000", C = a ? `shadow-${t.id}` : void 0, N = new on(e, e), j = [], {
          x: O,
          y: I,
          fontSize: E,
          wrappedLines: P,
          lineHeight: W,
          lineSpacing: F
        } = n;
        return P.forEach((L, B) => {
          if (!L.content.trim()) return;
          const { d: q } = N.getD(L.content, {
            x: 0,
            y: 0,
            fontSize: E,
            anchor: "left middle",
            letterSpacing: (t.styles.letterSpacing ?? 0) / E
            // 转换为相对值
          });
          if (!q) return;
          const G = I + B * (W + F), ye = s.textAlign || "center", oe = L.width;
          let V;
          switch (ye) {
            case "left":
              V = O;
              break;
            case "right":
              V = O - oe;
              break;
            case "center":
            default:
              V = O - oe / 2;
              break;
          }
          let ce = `translate(${V}, ${G})`;
          if (s.fontStyle === "italic" && (ce += " skewX(-12)"), j.push(
            /* @__PURE__ */ _.jsx(
              "path",
              {
                d: q,
                fill: "none",
                stroke: r ? y : "none",
                strokeWidth: g,
                strokeLinejoin: "round",
                strokeLinecap: "round",
                filter: C ? `url(#${C})` : void 0,
                transform: ce,
                style: {
                  vectorEffect: "non-scaling-stroke"
                }
              },
              `line-${B}`
            )
          ), s.fontWeight === "bold") {
            const U = Math.max(0.5, E * 0.01);
            [
              `translate(${V + U}, ${G})`,
              `translate(${V}, ${G + U})`,
              `translate(${V + U}, ${G + U})`
            ].forEach((Ce, Oe) => {
              let _e = Ce;
              s.fontStyle === "italic" && (_e += " skewX(-12)"), j.push(
                /* @__PURE__ */ _.jsx(
                  "path",
                  {
                    d: q,
                    fill: "none",
                    stroke: r ? y : "none",
                    strokeWidth: g,
                    strokeLinejoin: "round",
                    strokeLinecap: "round",
                    filter: C ? `url(#${C})` : void 0,
                    transform: _e,
                    style: {
                      vectorEffect: "non-scaling-stroke"
                    }
                  },
                  `line-bold-${B}-${Oe}`
                )
              );
            });
          }
          if (s.underlineEnabled) {
            const U = G + E * 0.5 + E * 0.1, xe = Math.max(1, E * 0.05);
            j.push(
              /* @__PURE__ */ _.jsx(
                "line",
                {
                  x1: V,
                  y1: U,
                  x2: V + oe,
                  y2: U,
                  stroke: r ? y : t.styles.color || "#ffffff",
                  strokeWidth: xe,
                  strokeLinecap: "round",
                  filter: C ? `url(#${C})` : void 0
                },
                `underline-${B}`
              )
            );
          }
        }), /* @__PURE__ */ _.jsx("g", { children: j });
      } catch (g) {
        return console.error("[增强文本渲染器] SVG路径渲染失败:", g), null;
      }
    },
    [t, r, a, s, n]
  ), d = o(), h = c(), v = Math.max(t.height, n.totalHeight + tt);
  return /* @__PURE__ */ _.jsxs(
    "svg",
    {
      ref: i,
      style: {
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 1,
        pointerEvents: "none"
      },
      viewBox: `0 0 ${t.width} ${v}`,
      preserveAspectRatio: "xMidYMid meet",
      children: [
        /* @__PURE__ */ _.jsx("defs", { children: d }),
        h
      ]
    }
  );
}, pn = ({ overlay: t, renderInfo: e }) => {
  const n = It(null), s = Ne((r, a, i, o, c, d, h) => {
    let v = i;
    if (d === "center" ? v = i - h / 2 : d === "right" && (v = i - h), c === 0) {
      r.fillText(a, v, o);
      return;
    }
    let g = v;
    for (let y = 0; y < a.length; y++) {
      const C = a[y];
      r.fillText(C, g, o);
      const N = r.measureText(C).width;
      g += N, y < a.length - 1 && (g += c);
    }
  }, []);
  return Le(
    () => {
      if (!n.current || t.width === 0)
        return;
      const { styles: r } = t, a = n.current;
      if (!a) return;
      const i = a.getContext("2d");
      if (!i) return;
      const {
        x: o,
        y: c,
        totalHeight: d,
        wrappedLines: h,
        lineHeight: v,
        letterSpacing: g,
        lineSpacing: y
      } = e, C = Math.max(t.height, d + tt);
      a.width = t.width * window.devicePixelRatio, a.height = C * window.devicePixelRatio, a.style.width = `${t.width}px`, a.style.height = `${C}px`, i.scale(window.devicePixelRatio, window.devicePixelRatio), i.clearRect(0, 0, t.width, C);
      const N = r.fontFamily || "Arial", j = `${t.styles.fontSize}px "${t.styles.fontFamily}", ${N}, sans-serif`;
      i.font = j, i.fillStyle = t.styles.color || "#ffffff", i.textBaseline = "middle", h.forEach((O, I) => {
        const E = c + I * (v + y);
        i.save();
        let P = 0;
        r.fontStyle === "italic" && (P = E * -0.2, i.transform(1, 0, -0.2, 1, 0, 0));
        const W = o - P, F = r.textAlign || "center";
        if (s(
          i,
          O.content,
          W,
          E,
          g,
          F,
          O.width
        ), r.fontWeight === "bold") {
          const L = Math.max(0.5, t.styles.fontSize * 0.01);
          s(
            i,
            O.content,
            W + L,
            E,
            g,
            F,
            O.width
          ), s(
            i,
            O.content,
            W,
            E + L,
            g,
            F,
            O.width
          ), s(
            i,
            O.content,
            W + L,
            E + L,
            g,
            F,
            O.width
          );
        }
        i.restore();
      });
    },
    [t, e, s]
  ), /* @__PURE__ */ _.jsx(
    "canvas",
    {
      ref: n,
      style: {
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 2,
        pointerEvents: "none"
      }
    }
  );
}, mn = ({ font: t, overlay: e, containerStyle: n }) => {
  const { styles: s } = e, r = !!s.backgroundImage, a = r && !!e.styles.bubbleTextRect, { buildCalcTextRenderInfoFunction: i } = hn(t, e), o = i(), c = Ye(
    () => o(),
    [o]
  ), d = {
    ...n,
    backgroundImage: r ? `url(${e.styles.backgroundImage})` : void 0,
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center"
  }, h = Ye(() => r ? {
    position: "absolute",
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    height: "60%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxSizing: "border-box"
  } : {}, [r]), v = () => /* @__PURE__ */ _.jsxs(
    "div",
    {
      style: {
        position: "relative",
        width: "100%",
        height: e.height,
        backgroundColor: s.backgroundColor,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        opacity: e.styles.textOpacity ?? 1
      },
      children: [
        /* @__PURE__ */ _.jsx(fn, { overlay: e, renderInfo: c, font: t }),
        /* @__PURE__ */ _.jsx(pn, { overlay: e, renderInfo: c })
      ]
    }
  ), g = () => a ? /* @__PURE__ */ _.jsx("div", { style: h, children: v() }) : v(), y = Math.max(e.height, c.totalHeight + tt);
  return /* @__PURE__ */ _.jsx(
    "div",
    {
      style: {
        ...d,
        position: "relative",
        overflow: "hidden",
        height: `${y}px`
      },
      children: g()
    }
  );
}, gn = ({
  overlay: t,
  containerStyle: e
}) => {
  const [n] = rt(() => $t()), [s, r] = rt(null), a = async () => {
    const i = t.src, o = t.styles.fontFamily;
    if (!i)
      return null;
    const c = await qt.load(t.src);
    try {
      const h = ((g) => {
        if (g.startsWith("http://") || g.startsWith("https://"))
          return g;
        const y = g.replace(/\\/g, "/");
        return y.startsWith("/") ? `file://${y}` : `file:///${y}`;
      })(i);
      if (!Array.from(document.fonts).find(
        (g) => g.family === o
      )) {
        const g = new FontFace(o, `url("${h}")`);
        await g.load(), document.fonts.add(g), console.log(`[CLOUD] 字体已加载到 DOM: ${o}`);
      }
    } catch (d) {
      console.warn("[CLOUD] DOM 字体加载失败，但 opentype.js 加载成功:", d);
    }
    r({
      font: c,
      fontFamily: o
    });
  };
  return Le(() => {
    s && $e(n);
  }, [s]), Le(() => {
    a();
  }, []), s ? /* @__PURE__ */ _.jsx(
    mn,
    {
      font: s.font,
      overlay: t,
      containerStyle: e
    }
  ) : null;
}, yn = et.memo(
  gn,
  (t, e) => {
    const n = t.overlay.id === e.overlay.id && t.overlay.content === e.overlay.content && t.overlay.src === e.overlay.src && t.overlay.width === e.overlay.width && t.overlay.left === e.overlay.left && t.overlay.top === e.overlay.top && t.overlay.rotation === e.overlay.rotation, s = JSON.stringify(t.overlay.styles) === JSON.stringify(e.overlay.styles), r = JSON.stringify(t.containerStyle) === JSON.stringify(e.containerStyle);
    return n && s && r;
  }
), xn = ({ overlay: t }) => {
  var o, c, d, h;
  const e = Re(), { playerMetadata: { fps: n } } = Te(), s = e >= t.durationInFrames - n, r = !s && ((o = t.styles.animation) != null && o.enter) ? (c = ue[t.styles.animation.enter]) == null ? void 0 : c.enter(
    e,
    t.durationInFrames
  ) : {}, a = s && ((d = t.styles.animation) != null && d.exit) ? (h = ue[t.styles.animation.exit]) == null ? void 0 : h.exit(
    e,
    t.durationInFrames
  ) : {}, i = {
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    // Center vertically
    textAlign: t.styles.textAlign,
    justifyContent: t.styles.textAlign === "center" ? "center" : t.styles.textAlign === "right" ? "flex-end" : "flex-start",
    overflow: "hidden",
    // 确保容器能够容纳换行文本
    boxSizing: "border-box",
    ...s ? a : r
  };
  return /* @__PURE__ */ _.jsx(
    yn,
    {
      overlay: t,
      containerStyle: i
    }
  );
}, _n = ({
  overlay: t,
  baseUrl: e
}) => {
  var I, E, P, W;
  const n = Re(), { playerMetadata: { fps: s } } = Te();
  let r = t.src;
  t.src.startsWith("/") && e ? r = `${e}${t.src}` : t.src.startsWith("/") && (r = Pt(t.src)), Le(() => {
    const F = $t("Loading video"), L = document.createElement("video");
    L.src = r;
    const B = () => {
      $e(F);
    }, q = (G) => {
      $e(F);
    };
    return L.addEventListener("loadedmetadata", B), L.addEventListener("error", q), () => {
      L.removeEventListener("loadedmetadata", B), L.removeEventListener("error", q), $e(F);
    };
  }, [t.src]);
  const a = t.fadeInDuration ? Math.round(t.fadeInDuration * s) : 0, i = t.fadeOutDuration ? Math.round(t.fadeOutDuration * s) : 0;
  let o = 1, c = 1;
  a > 0 && n < a && (o = n / a), i > 0 && n >= t.durationInFrames - i && (c = (t.durationInFrames - n) / i);
  let d = o * c;
  d = Math.max(0, Math.min(1, d));
  const h = n >= t.durationInFrames - s, v = !h && ((I = t.styles.animation) != null && I.enter) ? (E = ue[t.styles.animation.enter]) == null ? void 0 : E.enter(
    n,
    t.durationInFrames
  ) : {}, g = h && ((P = t.styles.animation) != null && P.exit) ? (W = ue[t.styles.animation.exit]) == null ? void 0 : W.exit(
    n,
    t.durationInFrames
  ) : {}, y = {
    width: "100%",
    height: "100%",
    objectFit: t.styles.objectFit || "cover",
    // 将基础不透明度与淡入淡出不透明度相乘，确保两者都生效
    opacity: (t.styles.opacity ?? 1) * d,
    transform: t.styles.transform || "none",
    borderRadius: t.styles.borderRadius || "0px",
    filter: t.styles.filter || "none",
    boxShadow: t.styles.boxShadow || "none",
    border: t.styles.border || "none",
    // 应用动画效果
    ...h ? g : v,
    // 添加平滑过渡效果
    transition: "opacity 0.1s ease-in-out"
  }, C = {
    width: "100%",
    height: "100%",
    padding: t.styles.padding || "0px",
    backgroundColor: t.styles.paddingBackgroundColor || "transparent",
    display: "flex",
    // Use flexbox for centering
    alignItems: "center",
    justifyContent: "center"
  }, N = (t.videoStartTime || 0) + (t.trimStart || 0), j = t.trimEnd ? Math.round(t.trimEnd * s) : 0, O = t.durationInFrames - j;
  return n >= O ? /* @__PURE__ */ _.jsx("div", { style: C }) : /* @__PURE__ */ _.jsx("div", { style: C, children: /* @__PURE__ */ _.jsx(
    Yt,
    {
      src: r,
      startFrom: N,
      style: y,
      volume: t.styles.volume ?? 1,
      playbackRate: t.speed ?? 1
    }
  ) });
};
var T;
(function(t) {
  t.assertEqual = (r) => {
  };
  function e(r) {
  }
  t.assertIs = e;
  function n(r) {
    throw new Error();
  }
  t.assertNever = n, t.arrayToEnum = (r) => {
    const a = {};
    for (const i of r)
      a[i] = i;
    return a;
  }, t.getValidEnumValues = (r) => {
    const a = t.objectKeys(r).filter((o) => typeof r[r[o]] != "number"), i = {};
    for (const o of a)
      i[o] = r[o];
    return t.objectValues(i);
  }, t.objectValues = (r) => t.objectKeys(r).map(function(a) {
    return r[a];
  }), t.objectKeys = typeof Object.keys == "function" ? (r) => Object.keys(r) : (r) => {
    const a = [];
    for (const i in r)
      Object.prototype.hasOwnProperty.call(r, i) && a.push(i);
    return a;
  }, t.find = (r, a) => {
    for (const i of r)
      if (a(i))
        return i;
  }, t.isInteger = typeof Number.isInteger == "function" ? (r) => Number.isInteger(r) : (r) => typeof r == "number" && Number.isFinite(r) && Math.floor(r) === r;
  function s(r, a = " | ") {
    return r.map((i) => typeof i == "string" ? `'${i}'` : i).join(a);
  }
  t.joinValues = s, t.jsonStringifyReplacer = (r, a) => typeof a == "bigint" ? a.toString() : a;
})(T || (T = {}));
var ft;
(function(t) {
  t.mergeShapes = (e, n) => ({
    ...e,
    ...n
    // second overwrites first
  });
})(ft || (ft = {}));
const p = T.arrayToEnum([
  "string",
  "nan",
  "number",
  "integer",
  "float",
  "boolean",
  "date",
  "bigint",
  "symbol",
  "function",
  "undefined",
  "null",
  "array",
  "object",
  "unknown",
  "promise",
  "void",
  "never",
  "map",
  "set"
]), Q = (t) => {
  switch (typeof t) {
    case "undefined":
      return p.undefined;
    case "string":
      return p.string;
    case "number":
      return Number.isNaN(t) ? p.nan : p.number;
    case "boolean":
      return p.boolean;
    case "function":
      return p.function;
    case "bigint":
      return p.bigint;
    case "symbol":
      return p.symbol;
    case "object":
      return Array.isArray(t) ? p.array : t === null ? p.null : t.then && typeof t.then == "function" && t.catch && typeof t.catch == "function" ? p.promise : typeof Map < "u" && t instanceof Map ? p.map : typeof Set < "u" && t instanceof Set ? p.set : typeof Date < "u" && t instanceof Date ? p.date : p.object;
    default:
      return p.unknown;
  }
}, u = T.arrayToEnum([
  "invalid_type",
  "invalid_literal",
  "custom",
  "invalid_union",
  "invalid_union_discriminator",
  "invalid_enum_value",
  "unrecognized_keys",
  "invalid_arguments",
  "invalid_return_type",
  "invalid_date",
  "invalid_string",
  "too_small",
  "too_big",
  "invalid_intersection_types",
  "not_multiple_of",
  "not_finite"
]);
class X extends Error {
  get errors() {
    return this.issues;
  }
  constructor(e) {
    super(), this.issues = [], this.addIssue = (s) => {
      this.issues = [...this.issues, s];
    }, this.addIssues = (s = []) => {
      this.issues = [...this.issues, ...s];
    };
    const n = new.target.prototype;
    Object.setPrototypeOf ? Object.setPrototypeOf(this, n) : this.__proto__ = n, this.name = "ZodError", this.issues = e;
  }
  format(e) {
    const n = e || function(a) {
      return a.message;
    }, s = { _errors: [] }, r = (a) => {
      for (const i of a.issues)
        if (i.code === "invalid_union")
          i.unionErrors.map(r);
        else if (i.code === "invalid_return_type")
          r(i.returnTypeError);
        else if (i.code === "invalid_arguments")
          r(i.argumentsError);
        else if (i.path.length === 0)
          s._errors.push(n(i));
        else {
          let o = s, c = 0;
          for (; c < i.path.length; ) {
            const d = i.path[c];
            c === i.path.length - 1 ? (o[d] = o[d] || { _errors: [] }, o[d]._errors.push(n(i))) : o[d] = o[d] || { _errors: [] }, o = o[d], c++;
          }
        }
    };
    return r(this), s;
  }
  static assert(e) {
    if (!(e instanceof X))
      throw new Error(`Not a ZodError: ${e}`);
  }
  toString() {
    return this.message;
  }
  get message() {
    return JSON.stringify(this.issues, T.jsonStringifyReplacer, 2);
  }
  get isEmpty() {
    return this.issues.length === 0;
  }
  flatten(e = (n) => n.message) {
    const n = {}, s = [];
    for (const r of this.issues)
      if (r.path.length > 0) {
        const a = r.path[0];
        n[a] = n[a] || [], n[a].push(e(r));
      } else
        s.push(e(r));
    return { formErrors: s, fieldErrors: n };
  }
  get formErrors() {
    return this.flatten();
  }
}
X.create = (t) => new X(t);
const Xe = (t, e) => {
  let n;
  switch (t.code) {
    case u.invalid_type:
      t.received === p.undefined ? n = "Required" : n = `Expected ${t.expected}, received ${t.received}`;
      break;
    case u.invalid_literal:
      n = `Invalid literal value, expected ${JSON.stringify(t.expected, T.jsonStringifyReplacer)}`;
      break;
    case u.unrecognized_keys:
      n = `Unrecognized key(s) in object: ${T.joinValues(t.keys, ", ")}`;
      break;
    case u.invalid_union:
      n = "Invalid input";
      break;
    case u.invalid_union_discriminator:
      n = `Invalid discriminator value. Expected ${T.joinValues(t.options)}`;
      break;
    case u.invalid_enum_value:
      n = `Invalid enum value. Expected ${T.joinValues(t.options)}, received '${t.received}'`;
      break;
    case u.invalid_arguments:
      n = "Invalid function arguments";
      break;
    case u.invalid_return_type:
      n = "Invalid function return type";
      break;
    case u.invalid_date:
      n = "Invalid date";
      break;
    case u.invalid_string:
      typeof t.validation == "object" ? "includes" in t.validation ? (n = `Invalid input: must include "${t.validation.includes}"`, typeof t.validation.position == "number" && (n = `${n} at one or more positions greater than or equal to ${t.validation.position}`)) : "startsWith" in t.validation ? n = `Invalid input: must start with "${t.validation.startsWith}"` : "endsWith" in t.validation ? n = `Invalid input: must end with "${t.validation.endsWith}"` : T.assertNever(t.validation) : t.validation !== "regex" ? n = `Invalid ${t.validation}` : n = "Invalid";
      break;
    case u.too_small:
      t.type === "array" ? n = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "more than"} ${t.minimum} element(s)` : t.type === "string" ? n = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at least" : "over"} ${t.minimum} character(s)` : t.type === "number" ? n = `Number must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "greater than "}${t.minimum}` : t.type === "bigint" ? n = `Number must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "greater than "}${t.minimum}` : t.type === "date" ? n = `Date must be ${t.exact ? "exactly equal to " : t.inclusive ? "greater than or equal to " : "greater than "}${new Date(Number(t.minimum))}` : n = "Invalid input";
      break;
    case u.too_big:
      t.type === "array" ? n = `Array must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "less than"} ${t.maximum} element(s)` : t.type === "string" ? n = `String must contain ${t.exact ? "exactly" : t.inclusive ? "at most" : "under"} ${t.maximum} character(s)` : t.type === "number" ? n = `Number must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` : t.type === "bigint" ? n = `BigInt must be ${t.exact ? "exactly" : t.inclusive ? "less than or equal to" : "less than"} ${t.maximum}` : t.type === "date" ? n = `Date must be ${t.exact ? "exactly" : t.inclusive ? "smaller than or equal to" : "smaller than"} ${new Date(Number(t.maximum))}` : n = "Invalid input";
      break;
    case u.custom:
      n = "Invalid input";
      break;
    case u.invalid_intersection_types:
      n = "Intersection results could not be merged";
      break;
    case u.not_multiple_of:
      n = `Number must be a multiple of ${t.multipleOf}`;
      break;
    case u.not_finite:
      n = "Number must be finite";
      break;
    default:
      n = e.defaultError, T.assertNever(t);
  }
  return { message: n };
};
let vn = Xe;
function bn() {
  return vn;
}
const kn = (t) => {
  const { data: e, path: n, errorMaps: s, issueData: r } = t, a = [...n, ...r.path || []], i = {
    ...r,
    path: a
  };
  if (r.message !== void 0)
    return {
      ...r,
      path: a,
      message: r.message
    };
  let o = "";
  const c = s.filter((d) => !!d).slice().reverse();
  for (const d of c)
    o = d(i, { data: e, defaultError: o }).message;
  return {
    ...r,
    path: a,
    message: o
  };
};
function f(t, e) {
  const n = bn(), s = kn({
    issueData: e,
    data: t.data,
    path: t.path,
    errorMaps: [
      t.common.contextualErrorMap,
      // contextual error map is first priority
      t.schemaErrorMap,
      // then schema-bound map if available
      n,
      // then global override map
      n === Xe ? void 0 : Xe
      // then global default map
    ].filter((r) => !!r)
  });
  t.common.issues.push(s);
}
class D {
  constructor() {
    this.value = "valid";
  }
  dirty() {
    this.value === "valid" && (this.value = "dirty");
  }
  abort() {
    this.value !== "aborted" && (this.value = "aborted");
  }
  static mergeArray(e, n) {
    const s = [];
    for (const r of n) {
      if (r.status === "aborted")
        return b;
      r.status === "dirty" && e.dirty(), s.push(r.value);
    }
    return { status: e.value, value: s };
  }
  static async mergeObjectAsync(e, n) {
    const s = [];
    for (const r of n) {
      const a = await r.key, i = await r.value;
      s.push({
        key: a,
        value: i
      });
    }
    return D.mergeObjectSync(e, s);
  }
  static mergeObjectSync(e, n) {
    const s = {};
    for (const r of n) {
      const { key: a, value: i } = r;
      if (a.status === "aborted" || i.status === "aborted")
        return b;
      a.status === "dirty" && e.dirty(), i.status === "dirty" && e.dirty(), a.value !== "__proto__" && (typeof i.value < "u" || r.alwaysSet) && (s[a.value] = i.value);
    }
    return { status: e.value, value: s };
  }
}
const b = Object.freeze({
  status: "aborted"
}), ke = (t) => ({ status: "dirty", value: t }), z = (t) => ({ status: "valid", value: t }), pt = (t) => t.status === "aborted", mt = (t) => t.status === "dirty", he = (t) => t.status === "valid", Pe = (t) => typeof Promise < "u" && t instanceof Promise;
var m;
(function(t) {
  t.errToObj = (e) => typeof e == "string" ? { message: e } : e || {}, t.toString = (e) => typeof e == "string" ? e : e == null ? void 0 : e.message;
})(m || (m = {}));
class te {
  constructor(e, n, s, r) {
    this._cachedPath = [], this.parent = e, this.data = n, this._path = s, this._key = r;
  }
  get path() {
    return this._cachedPath.length || (Array.isArray(this._key) ? this._cachedPath.push(...this._path, ...this._key) : this._cachedPath.push(...this._path, this._key)), this._cachedPath;
  }
}
const gt = (t, e) => {
  if (he(e))
    return { success: !0, data: e.value };
  if (!t.common.issues.length)
    throw new Error("Validation failed but no issues detected.");
  return {
    success: !1,
    get error() {
      if (this._error)
        return this._error;
      const n = new X(t.common.issues);
      return this._error = n, this._error;
    }
  };
};
function S(t) {
  if (!t)
    return {};
  const { errorMap: e, invalid_type_error: n, required_error: s, description: r } = t;
  if (e && (n || s))
    throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);
  return e ? { errorMap: e, description: r } : { errorMap: (i, o) => {
    const { message: c } = t;
    return i.code === "invalid_enum_value" ? { message: c ?? o.defaultError } : typeof o.data > "u" ? { message: c ?? s ?? o.defaultError } : i.code !== "invalid_type" ? { message: o.defaultError } : { message: c ?? n ?? o.defaultError };
  }, description: r };
}
class R {
  get description() {
    return this._def.description;
  }
  _getType(e) {
    return Q(e.data);
  }
  _getOrReturnCtx(e, n) {
    return n || {
      common: e.parent.common,
      data: e.data,
      parsedType: Q(e.data),
      schemaErrorMap: this._def.errorMap,
      path: e.path,
      parent: e.parent
    };
  }
  _processInputParams(e) {
    return {
      status: new D(),
      ctx: {
        common: e.parent.common,
        data: e.data,
        parsedType: Q(e.data),
        schemaErrorMap: this._def.errorMap,
        path: e.path,
        parent: e.parent
      }
    };
  }
  _parseSync(e) {
    const n = this._parse(e);
    if (Pe(n))
      throw new Error("Synchronous parse encountered promise.");
    return n;
  }
  _parseAsync(e) {
    const n = this._parse(e);
    return Promise.resolve(n);
  }
  parse(e, n) {
    const s = this.safeParse(e, n);
    if (s.success)
      return s.data;
    throw s.error;
  }
  safeParse(e, n) {
    const s = {
      common: {
        issues: [],
        async: (n == null ? void 0 : n.async) ?? !1,
        contextualErrorMap: n == null ? void 0 : n.errorMap
      },
      path: (n == null ? void 0 : n.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: e,
      parsedType: Q(e)
    }, r = this._parseSync({ data: e, path: s.path, parent: s });
    return gt(s, r);
  }
  "~validate"(e) {
    var s, r;
    const n = {
      common: {
        issues: [],
        async: !!this["~standard"].async
      },
      path: [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: e,
      parsedType: Q(e)
    };
    if (!this["~standard"].async)
      try {
        const a = this._parseSync({ data: e, path: [], parent: n });
        return he(a) ? {
          value: a.value
        } : {
          issues: n.common.issues
        };
      } catch (a) {
        (r = (s = a == null ? void 0 : a.message) == null ? void 0 : s.toLowerCase()) != null && r.includes("encountered") && (this["~standard"].async = !0), n.common = {
          issues: [],
          async: !0
        };
      }
    return this._parseAsync({ data: e, path: [], parent: n }).then((a) => he(a) ? {
      value: a.value
    } : {
      issues: n.common.issues
    });
  }
  async parseAsync(e, n) {
    const s = await this.safeParseAsync(e, n);
    if (s.success)
      return s.data;
    throw s.error;
  }
  async safeParseAsync(e, n) {
    const s = {
      common: {
        issues: [],
        contextualErrorMap: n == null ? void 0 : n.errorMap,
        async: !0
      },
      path: (n == null ? void 0 : n.path) || [],
      schemaErrorMap: this._def.errorMap,
      parent: null,
      data: e,
      parsedType: Q(e)
    }, r = this._parse({ data: e, path: s.path, parent: s }), a = await (Pe(r) ? r : Promise.resolve(r));
    return gt(s, a);
  }
  refine(e, n) {
    const s = (r) => typeof n == "string" || typeof n > "u" ? { message: n } : typeof n == "function" ? n(r) : n;
    return this._refinement((r, a) => {
      const i = e(r), o = () => a.addIssue({
        code: u.custom,
        ...s(r)
      });
      return typeof Promise < "u" && i instanceof Promise ? i.then((c) => c ? !0 : (o(), !1)) : i ? !0 : (o(), !1);
    });
  }
  refinement(e, n) {
    return this._refinement((s, r) => e(s) ? !0 : (r.addIssue(typeof n == "function" ? n(s, r) : n), !1));
  }
  _refinement(e) {
    return new me({
      schema: this,
      typeName: k.ZodEffects,
      effect: { type: "refinement", refinement: e }
    });
  }
  superRefine(e) {
    return this._refinement(e);
  }
  constructor(e) {
    this.spa = this.safeParseAsync, this._def = e, this.parse = this.parse.bind(this), this.safeParse = this.safeParse.bind(this), this.parseAsync = this.parseAsync.bind(this), this.safeParseAsync = this.safeParseAsync.bind(this), this.spa = this.spa.bind(this), this.refine = this.refine.bind(this), this.refinement = this.refinement.bind(this), this.superRefine = this.superRefine.bind(this), this.optional = this.optional.bind(this), this.nullable = this.nullable.bind(this), this.nullish = this.nullish.bind(this), this.array = this.array.bind(this), this.promise = this.promise.bind(this), this.or = this.or.bind(this), this.and = this.and.bind(this), this.transform = this.transform.bind(this), this.brand = this.brand.bind(this), this.default = this.default.bind(this), this.catch = this.catch.bind(this), this.describe = this.describe.bind(this), this.pipe = this.pipe.bind(this), this.readonly = this.readonly.bind(this), this.isNullable = this.isNullable.bind(this), this.isOptional = this.isOptional.bind(this), this["~standard"] = {
      version: 1,
      vendor: "zod",
      validate: (n) => this["~validate"](n)
    };
  }
  optional() {
    return ee.create(this, this._def);
  }
  nullable() {
    return ge.create(this, this._def);
  }
  nullish() {
    return this.nullable().optional();
  }
  array() {
    return H.create(this);
  }
  promise() {
    return We.create(this, this._def);
  }
  or(e) {
    return Ze.create([this, e], this._def);
  }
  and(e) {
    return Me.create(this, e, this._def);
  }
  transform(e) {
    return new me({
      ...S(this._def),
      schema: this,
      typeName: k.ZodEffects,
      effect: { type: "transform", transform: e }
    });
  }
  default(e) {
    const n = typeof e == "function" ? e : () => e;
    return new Je({
      ...S(this._def),
      innerType: this,
      defaultValue: n,
      typeName: k.ZodDefault
    });
  }
  brand() {
    return new Un({
      typeName: k.ZodBranded,
      type: this,
      ...S(this._def)
    });
  }
  catch(e) {
    const n = typeof e == "function" ? e : () => e;
    return new Qe({
      ...S(this._def),
      innerType: this,
      catchValue: n,
      typeName: k.ZodCatch
    });
  }
  describe(e) {
    const n = this.constructor;
    return new n({
      ...this._def,
      description: e
    });
  }
  pipe(e) {
    return nt.create(this, e);
  }
  readonly() {
    return Ke.create(this);
  }
  isOptional() {
    return this.safeParse(void 0).success;
  }
  isNullable() {
    return this.safeParse(null).success;
  }
}
const wn = /^c[^\s-]{8,}$/i, Sn = /^[0-9a-z]+$/, Rn = /^[0-9A-HJKMNP-TV-Z]{26}$/i, Tn = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i, Cn = /^[a-z0-9_-]{21}$/i, On = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/, En = /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/, An = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i, jn = "^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";
let Ue;
const In = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/, $n = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/, Nn = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/, Ln = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/, Pn = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/, Fn = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/, Ft = "((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))", Zn = new RegExp(`^${Ft}$`);
function Zt(t) {
  let e = "[0-5]\\d";
  t.precision ? e = `${e}\\.\\d{${t.precision}}` : t.precision == null && (e = `${e}(\\.\\d+)?`);
  const n = t.precision ? "+" : "?";
  return `([01]\\d|2[0-3]):[0-5]\\d(:${e})${n}`;
}
function Mn(t) {
  return new RegExp(`^${Zt(t)}$`);
}
function Wn(t) {
  let e = `${Ft}T${Zt(t)}`;
  const n = [];
  return n.push(t.local ? "Z?" : "Z"), t.offset && n.push("([+-]\\d{2}:?\\d{2})"), e = `${e}(${n.join("|")})`, new RegExp(`^${e}$`);
}
function Dn(t, e) {
  return !!((e === "v4" || !e) && In.test(t) || (e === "v6" || !e) && Nn.test(t));
}
function zn(t, e) {
  if (!On.test(t))
    return !1;
  try {
    const [n] = t.split(".");
    if (!n)
      return !1;
    const s = n.replace(/-/g, "+").replace(/_/g, "/").padEnd(n.length + (4 - n.length % 4) % 4, "="), r = JSON.parse(atob(s));
    return !(typeof r != "object" || r === null || "typ" in r && (r == null ? void 0 : r.typ) !== "JWT" || !r.alg || e && r.alg !== e);
  } catch {
    return !1;
  }
}
function Vn(t, e) {
  return !!((e === "v4" || !e) && $n.test(t) || (e === "v6" || !e) && Ln.test(t));
}
class K extends R {
  _parse(e) {
    if (this._def.coerce && (e.data = String(e.data)), this._getType(e) !== p.string) {
      const a = this._getOrReturnCtx(e);
      return f(a, {
        code: u.invalid_type,
        expected: p.string,
        received: a.parsedType
      }), b;
    }
    const s = new D();
    let r;
    for (const a of this._def.checks)
      if (a.kind === "min")
        e.data.length < a.value && (r = this._getOrReturnCtx(e, r), f(r, {
          code: u.too_small,
          minimum: a.value,
          type: "string",
          inclusive: !0,
          exact: !1,
          message: a.message
        }), s.dirty());
      else if (a.kind === "max")
        e.data.length > a.value && (r = this._getOrReturnCtx(e, r), f(r, {
          code: u.too_big,
          maximum: a.value,
          type: "string",
          inclusive: !0,
          exact: !1,
          message: a.message
        }), s.dirty());
      else if (a.kind === "length") {
        const i = e.data.length > a.value, o = e.data.length < a.value;
        (i || o) && (r = this._getOrReturnCtx(e, r), i ? f(r, {
          code: u.too_big,
          maximum: a.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: a.message
        }) : o && f(r, {
          code: u.too_small,
          minimum: a.value,
          type: "string",
          inclusive: !0,
          exact: !0,
          message: a.message
        }), s.dirty());
      } else if (a.kind === "email")
        An.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "email",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "emoji")
        Ue || (Ue = new RegExp(jn, "u")), Ue.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "emoji",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "uuid")
        Tn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "uuid",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "nanoid")
        Cn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "nanoid",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "cuid")
        wn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "cuid",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "cuid2")
        Sn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "cuid2",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "ulid")
        Rn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
          validation: "ulid",
          code: u.invalid_string,
          message: a.message
        }), s.dirty());
      else if (a.kind === "url")
        try {
          new URL(e.data);
        } catch {
          r = this._getOrReturnCtx(e, r), f(r, {
            validation: "url",
            code: u.invalid_string,
            message: a.message
          }), s.dirty();
        }
      else a.kind === "regex" ? (a.regex.lastIndex = 0, a.regex.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "regex",
        code: u.invalid_string,
        message: a.message
      }), s.dirty())) : a.kind === "trim" ? e.data = e.data.trim() : a.kind === "includes" ? e.data.includes(a.value, a.position) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: { includes: a.value, position: a.position },
        message: a.message
      }), s.dirty()) : a.kind === "toLowerCase" ? e.data = e.data.toLowerCase() : a.kind === "toUpperCase" ? e.data = e.data.toUpperCase() : a.kind === "startsWith" ? e.data.startsWith(a.value) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: { startsWith: a.value },
        message: a.message
      }), s.dirty()) : a.kind === "endsWith" ? e.data.endsWith(a.value) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: { endsWith: a.value },
        message: a.message
      }), s.dirty()) : a.kind === "datetime" ? Wn(a).test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: "datetime",
        message: a.message
      }), s.dirty()) : a.kind === "date" ? Zn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: "date",
        message: a.message
      }), s.dirty()) : a.kind === "time" ? Mn(a).test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.invalid_string,
        validation: "time",
        message: a.message
      }), s.dirty()) : a.kind === "duration" ? En.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "duration",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : a.kind === "ip" ? Dn(e.data, a.version) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "ip",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : a.kind === "jwt" ? zn(e.data, a.alg) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "jwt",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : a.kind === "cidr" ? Vn(e.data, a.version) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "cidr",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : a.kind === "base64" ? Pn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "base64",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : a.kind === "base64url" ? Fn.test(e.data) || (r = this._getOrReturnCtx(e, r), f(r, {
        validation: "base64url",
        code: u.invalid_string,
        message: a.message
      }), s.dirty()) : T.assertNever(a);
    return { status: s.value, value: e.data };
  }
  _regex(e, n, s) {
    return this.refinement((r) => e.test(r), {
      validation: n,
      code: u.invalid_string,
      ...m.errToObj(s)
    });
  }
  _addCheck(e) {
    return new K({
      ...this._def,
      checks: [...this._def.checks, e]
    });
  }
  email(e) {
    return this._addCheck({ kind: "email", ...m.errToObj(e) });
  }
  url(e) {
    return this._addCheck({ kind: "url", ...m.errToObj(e) });
  }
  emoji(e) {
    return this._addCheck({ kind: "emoji", ...m.errToObj(e) });
  }
  uuid(e) {
    return this._addCheck({ kind: "uuid", ...m.errToObj(e) });
  }
  nanoid(e) {
    return this._addCheck({ kind: "nanoid", ...m.errToObj(e) });
  }
  cuid(e) {
    return this._addCheck({ kind: "cuid", ...m.errToObj(e) });
  }
  cuid2(e) {
    return this._addCheck({ kind: "cuid2", ...m.errToObj(e) });
  }
  ulid(e) {
    return this._addCheck({ kind: "ulid", ...m.errToObj(e) });
  }
  base64(e) {
    return this._addCheck({ kind: "base64", ...m.errToObj(e) });
  }
  base64url(e) {
    return this._addCheck({
      kind: "base64url",
      ...m.errToObj(e)
    });
  }
  jwt(e) {
    return this._addCheck({ kind: "jwt", ...m.errToObj(e) });
  }
  ip(e) {
    return this._addCheck({ kind: "ip", ...m.errToObj(e) });
  }
  cidr(e) {
    return this._addCheck({ kind: "cidr", ...m.errToObj(e) });
  }
  datetime(e) {
    return typeof e == "string" ? this._addCheck({
      kind: "datetime",
      precision: null,
      offset: !1,
      local: !1,
      message: e
    }) : this._addCheck({
      kind: "datetime",
      precision: typeof (e == null ? void 0 : e.precision) > "u" ? null : e == null ? void 0 : e.precision,
      offset: (e == null ? void 0 : e.offset) ?? !1,
      local: (e == null ? void 0 : e.local) ?? !1,
      ...m.errToObj(e == null ? void 0 : e.message)
    });
  }
  date(e) {
    return this._addCheck({ kind: "date", message: e });
  }
  time(e) {
    return typeof e == "string" ? this._addCheck({
      kind: "time",
      precision: null,
      message: e
    }) : this._addCheck({
      kind: "time",
      precision: typeof (e == null ? void 0 : e.precision) > "u" ? null : e == null ? void 0 : e.precision,
      ...m.errToObj(e == null ? void 0 : e.message)
    });
  }
  duration(e) {
    return this._addCheck({ kind: "duration", ...m.errToObj(e) });
  }
  regex(e, n) {
    return this._addCheck({
      kind: "regex",
      regex: e,
      ...m.errToObj(n)
    });
  }
  includes(e, n) {
    return this._addCheck({
      kind: "includes",
      value: e,
      position: n == null ? void 0 : n.position,
      ...m.errToObj(n == null ? void 0 : n.message)
    });
  }
  startsWith(e, n) {
    return this._addCheck({
      kind: "startsWith",
      value: e,
      ...m.errToObj(n)
    });
  }
  endsWith(e, n) {
    return this._addCheck({
      kind: "endsWith",
      value: e,
      ...m.errToObj(n)
    });
  }
  min(e, n) {
    return this._addCheck({
      kind: "min",
      value: e,
      ...m.errToObj(n)
    });
  }
  max(e, n) {
    return this._addCheck({
      kind: "max",
      value: e,
      ...m.errToObj(n)
    });
  }
  length(e, n) {
    return this._addCheck({
      kind: "length",
      value: e,
      ...m.errToObj(n)
    });
  }
  /**
   * Equivalent to `.min(1)`
   */
  nonempty(e) {
    return this.min(1, m.errToObj(e));
  }
  trim() {
    return new K({
      ...this._def,
      checks: [...this._def.checks, { kind: "trim" }]
    });
  }
  toLowerCase() {
    return new K({
      ...this._def,
      checks: [...this._def.checks, { kind: "toLowerCase" }]
    });
  }
  toUpperCase() {
    return new K({
      ...this._def,
      checks: [...this._def.checks, { kind: "toUpperCase" }]
    });
  }
  get isDatetime() {
    return !!this._def.checks.find((e) => e.kind === "datetime");
  }
  get isDate() {
    return !!this._def.checks.find((e) => e.kind === "date");
  }
  get isTime() {
    return !!this._def.checks.find((e) => e.kind === "time");
  }
  get isDuration() {
    return !!this._def.checks.find((e) => e.kind === "duration");
  }
  get isEmail() {
    return !!this._def.checks.find((e) => e.kind === "email");
  }
  get isURL() {
    return !!this._def.checks.find((e) => e.kind === "url");
  }
  get isEmoji() {
    return !!this._def.checks.find((e) => e.kind === "emoji");
  }
  get isUUID() {
    return !!this._def.checks.find((e) => e.kind === "uuid");
  }
  get isNANOID() {
    return !!this._def.checks.find((e) => e.kind === "nanoid");
  }
  get isCUID() {
    return !!this._def.checks.find((e) => e.kind === "cuid");
  }
  get isCUID2() {
    return !!this._def.checks.find((e) => e.kind === "cuid2");
  }
  get isULID() {
    return !!this._def.checks.find((e) => e.kind === "ulid");
  }
  get isIP() {
    return !!this._def.checks.find((e) => e.kind === "ip");
  }
  get isCIDR() {
    return !!this._def.checks.find((e) => e.kind === "cidr");
  }
  get isBase64() {
    return !!this._def.checks.find((e) => e.kind === "base64");
  }
  get isBase64url() {
    return !!this._def.checks.find((e) => e.kind === "base64url");
  }
  get minLength() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "min" && (e === null || n.value > e) && (e = n.value);
    return e;
  }
  get maxLength() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "max" && (e === null || n.value < e) && (e = n.value);
    return e;
  }
}
K.create = (t) => new K({
  checks: [],
  typeName: k.ZodString,
  coerce: (t == null ? void 0 : t.coerce) ?? !1,
  ...S(t)
});
function Bn(t, e) {
  const n = (t.toString().split(".")[1] || "").length, s = (e.toString().split(".")[1] || "").length, r = n > s ? n : s, a = Number.parseInt(t.toFixed(r).replace(".", "")), i = Number.parseInt(e.toFixed(r).replace(".", ""));
  return a % i / 10 ** r;
}
class fe extends R {
  constructor() {
    super(...arguments), this.min = this.gte, this.max = this.lte, this.step = this.multipleOf;
  }
  _parse(e) {
    if (this._def.coerce && (e.data = Number(e.data)), this._getType(e) !== p.number) {
      const a = this._getOrReturnCtx(e);
      return f(a, {
        code: u.invalid_type,
        expected: p.number,
        received: a.parsedType
      }), b;
    }
    let s;
    const r = new D();
    for (const a of this._def.checks)
      a.kind === "int" ? T.isInteger(e.data) || (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.invalid_type,
        expected: "integer",
        received: "float",
        message: a.message
      }), r.dirty()) : a.kind === "min" ? (a.inclusive ? e.data < a.value : e.data <= a.value) && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.too_small,
        minimum: a.value,
        type: "number",
        inclusive: a.inclusive,
        exact: !1,
        message: a.message
      }), r.dirty()) : a.kind === "max" ? (a.inclusive ? e.data > a.value : e.data >= a.value) && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.too_big,
        maximum: a.value,
        type: "number",
        inclusive: a.inclusive,
        exact: !1,
        message: a.message
      }), r.dirty()) : a.kind === "multipleOf" ? Bn(e.data, a.value) !== 0 && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.not_multiple_of,
        multipleOf: a.value,
        message: a.message
      }), r.dirty()) : a.kind === "finite" ? Number.isFinite(e.data) || (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.not_finite,
        message: a.message
      }), r.dirty()) : T.assertNever(a);
    return { status: r.value, value: e.data };
  }
  gte(e, n) {
    return this.setLimit("min", e, !0, m.toString(n));
  }
  gt(e, n) {
    return this.setLimit("min", e, !1, m.toString(n));
  }
  lte(e, n) {
    return this.setLimit("max", e, !0, m.toString(n));
  }
  lt(e, n) {
    return this.setLimit("max", e, !1, m.toString(n));
  }
  setLimit(e, n, s, r) {
    return new fe({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind: e,
          value: n,
          inclusive: s,
          message: m.toString(r)
        }
      ]
    });
  }
  _addCheck(e) {
    return new fe({
      ...this._def,
      checks: [...this._def.checks, e]
    });
  }
  int(e) {
    return this._addCheck({
      kind: "int",
      message: m.toString(e)
    });
  }
  positive(e) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !1,
      message: m.toString(e)
    });
  }
  negative(e) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !1,
      message: m.toString(e)
    });
  }
  nonpositive(e) {
    return this._addCheck({
      kind: "max",
      value: 0,
      inclusive: !0,
      message: m.toString(e)
    });
  }
  nonnegative(e) {
    return this._addCheck({
      kind: "min",
      value: 0,
      inclusive: !0,
      message: m.toString(e)
    });
  }
  multipleOf(e, n) {
    return this._addCheck({
      kind: "multipleOf",
      value: e,
      message: m.toString(n)
    });
  }
  finite(e) {
    return this._addCheck({
      kind: "finite",
      message: m.toString(e)
    });
  }
  safe(e) {
    return this._addCheck({
      kind: "min",
      inclusive: !0,
      value: Number.MIN_SAFE_INTEGER,
      message: m.toString(e)
    })._addCheck({
      kind: "max",
      inclusive: !0,
      value: Number.MAX_SAFE_INTEGER,
      message: m.toString(e)
    });
  }
  get minValue() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "min" && (e === null || n.value > e) && (e = n.value);
    return e;
  }
  get maxValue() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "max" && (e === null || n.value < e) && (e = n.value);
    return e;
  }
  get isInt() {
    return !!this._def.checks.find((e) => e.kind === "int" || e.kind === "multipleOf" && T.isInteger(e.value));
  }
  get isFinite() {
    let e = null, n = null;
    for (const s of this._def.checks) {
      if (s.kind === "finite" || s.kind === "int" || s.kind === "multipleOf")
        return !0;
      s.kind === "min" ? (n === null || s.value > n) && (n = s.value) : s.kind === "max" && (e === null || s.value < e) && (e = s.value);
    }
    return Number.isFinite(n) && Number.isFinite(e);
  }
}
fe.create = (t) => new fe({
  checks: [],
  typeName: k.ZodNumber,
  coerce: (t == null ? void 0 : t.coerce) || !1,
  ...S(t)
});
class we extends R {
  constructor() {
    super(...arguments), this.min = this.gte, this.max = this.lte;
  }
  _parse(e) {
    if (this._def.coerce)
      try {
        e.data = BigInt(e.data);
      } catch {
        return this._getInvalidInput(e);
      }
    if (this._getType(e) !== p.bigint)
      return this._getInvalidInput(e);
    let s;
    const r = new D();
    for (const a of this._def.checks)
      a.kind === "min" ? (a.inclusive ? e.data < a.value : e.data <= a.value) && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.too_small,
        type: "bigint",
        minimum: a.value,
        inclusive: a.inclusive,
        message: a.message
      }), r.dirty()) : a.kind === "max" ? (a.inclusive ? e.data > a.value : e.data >= a.value) && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.too_big,
        type: "bigint",
        maximum: a.value,
        inclusive: a.inclusive,
        message: a.message
      }), r.dirty()) : a.kind === "multipleOf" ? e.data % a.value !== BigInt(0) && (s = this._getOrReturnCtx(e, s), f(s, {
        code: u.not_multiple_of,
        multipleOf: a.value,
        message: a.message
      }), r.dirty()) : T.assertNever(a);
    return { status: r.value, value: e.data };
  }
  _getInvalidInput(e) {
    const n = this._getOrReturnCtx(e);
    return f(n, {
      code: u.invalid_type,
      expected: p.bigint,
      received: n.parsedType
    }), b;
  }
  gte(e, n) {
    return this.setLimit("min", e, !0, m.toString(n));
  }
  gt(e, n) {
    return this.setLimit("min", e, !1, m.toString(n));
  }
  lte(e, n) {
    return this.setLimit("max", e, !0, m.toString(n));
  }
  lt(e, n) {
    return this.setLimit("max", e, !1, m.toString(n));
  }
  setLimit(e, n, s, r) {
    return new we({
      ...this._def,
      checks: [
        ...this._def.checks,
        {
          kind: e,
          value: n,
          inclusive: s,
          message: m.toString(r)
        }
      ]
    });
  }
  _addCheck(e) {
    return new we({
      ...this._def,
      checks: [...this._def.checks, e]
    });
  }
  positive(e) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !1,
      message: m.toString(e)
    });
  }
  negative(e) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !1,
      message: m.toString(e)
    });
  }
  nonpositive(e) {
    return this._addCheck({
      kind: "max",
      value: BigInt(0),
      inclusive: !0,
      message: m.toString(e)
    });
  }
  nonnegative(e) {
    return this._addCheck({
      kind: "min",
      value: BigInt(0),
      inclusive: !0,
      message: m.toString(e)
    });
  }
  multipleOf(e, n) {
    return this._addCheck({
      kind: "multipleOf",
      value: e,
      message: m.toString(n)
    });
  }
  get minValue() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "min" && (e === null || n.value > e) && (e = n.value);
    return e;
  }
  get maxValue() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "max" && (e === null || n.value < e) && (e = n.value);
    return e;
  }
}
we.create = (t) => new we({
  checks: [],
  typeName: k.ZodBigInt,
  coerce: (t == null ? void 0 : t.coerce) ?? !1,
  ...S(t)
});
class yt extends R {
  _parse(e) {
    if (this._def.coerce && (e.data = !!e.data), this._getType(e) !== p.boolean) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.boolean,
        received: s.parsedType
      }), b;
    }
    return z(e.data);
  }
}
yt.create = (t) => new yt({
  typeName: k.ZodBoolean,
  coerce: (t == null ? void 0 : t.coerce) || !1,
  ...S(t)
});
class Fe extends R {
  _parse(e) {
    if (this._def.coerce && (e.data = new Date(e.data)), this._getType(e) !== p.date) {
      const a = this._getOrReturnCtx(e);
      return f(a, {
        code: u.invalid_type,
        expected: p.date,
        received: a.parsedType
      }), b;
    }
    if (Number.isNaN(e.data.getTime())) {
      const a = this._getOrReturnCtx(e);
      return f(a, {
        code: u.invalid_date
      }), b;
    }
    const s = new D();
    let r;
    for (const a of this._def.checks)
      a.kind === "min" ? e.data.getTime() < a.value && (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.too_small,
        message: a.message,
        inclusive: !0,
        exact: !1,
        minimum: a.value,
        type: "date"
      }), s.dirty()) : a.kind === "max" ? e.data.getTime() > a.value && (r = this._getOrReturnCtx(e, r), f(r, {
        code: u.too_big,
        message: a.message,
        inclusive: !0,
        exact: !1,
        maximum: a.value,
        type: "date"
      }), s.dirty()) : T.assertNever(a);
    return {
      status: s.value,
      value: new Date(e.data.getTime())
    };
  }
  _addCheck(e) {
    return new Fe({
      ...this._def,
      checks: [...this._def.checks, e]
    });
  }
  min(e, n) {
    return this._addCheck({
      kind: "min",
      value: e.getTime(),
      message: m.toString(n)
    });
  }
  max(e, n) {
    return this._addCheck({
      kind: "max",
      value: e.getTime(),
      message: m.toString(n)
    });
  }
  get minDate() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "min" && (e === null || n.value > e) && (e = n.value);
    return e != null ? new Date(e) : null;
  }
  get maxDate() {
    let e = null;
    for (const n of this._def.checks)
      n.kind === "max" && (e === null || n.value < e) && (e = n.value);
    return e != null ? new Date(e) : null;
  }
}
Fe.create = (t) => new Fe({
  checks: [],
  coerce: (t == null ? void 0 : t.coerce) || !1,
  typeName: k.ZodDate,
  ...S(t)
});
class xt extends R {
  _parse(e) {
    if (this._getType(e) !== p.symbol) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.symbol,
        received: s.parsedType
      }), b;
    }
    return z(e.data);
  }
}
xt.create = (t) => new xt({
  typeName: k.ZodSymbol,
  ...S(t)
});
class _t extends R {
  _parse(e) {
    if (this._getType(e) !== p.undefined) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.undefined,
        received: s.parsedType
      }), b;
    }
    return z(e.data);
  }
}
_t.create = (t) => new _t({
  typeName: k.ZodUndefined,
  ...S(t)
});
class vt extends R {
  _parse(e) {
    if (this._getType(e) !== p.null) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.null,
        received: s.parsedType
      }), b;
    }
    return z(e.data);
  }
}
vt.create = (t) => new vt({
  typeName: k.ZodNull,
  ...S(t)
});
class qe extends R {
  constructor() {
    super(...arguments), this._any = !0;
  }
  _parse(e) {
    return z(e.data);
  }
}
qe.create = (t) => new qe({
  typeName: k.ZodAny,
  ...S(t)
});
class bt extends R {
  constructor() {
    super(...arguments), this._unknown = !0;
  }
  _parse(e) {
    return z(e.data);
  }
}
bt.create = (t) => new bt({
  typeName: k.ZodUnknown,
  ...S(t)
});
class ne extends R {
  _parse(e) {
    const n = this._getOrReturnCtx(e);
    return f(n, {
      code: u.invalid_type,
      expected: p.never,
      received: n.parsedType
    }), b;
  }
}
ne.create = (t) => new ne({
  typeName: k.ZodNever,
  ...S(t)
});
class kt extends R {
  _parse(e) {
    if (this._getType(e) !== p.undefined) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.void,
        received: s.parsedType
      }), b;
    }
    return z(e.data);
  }
}
kt.create = (t) => new kt({
  typeName: k.ZodVoid,
  ...S(t)
});
class H extends R {
  _parse(e) {
    const { ctx: n, status: s } = this._processInputParams(e), r = this._def;
    if (n.parsedType !== p.array)
      return f(n, {
        code: u.invalid_type,
        expected: p.array,
        received: n.parsedType
      }), b;
    if (r.exactLength !== null) {
      const i = n.data.length > r.exactLength.value, o = n.data.length < r.exactLength.value;
      (i || o) && (f(n, {
        code: i ? u.too_big : u.too_small,
        minimum: o ? r.exactLength.value : void 0,
        maximum: i ? r.exactLength.value : void 0,
        type: "array",
        inclusive: !0,
        exact: !0,
        message: r.exactLength.message
      }), s.dirty());
    }
    if (r.minLength !== null && n.data.length < r.minLength.value && (f(n, {
      code: u.too_small,
      minimum: r.minLength.value,
      type: "array",
      inclusive: !0,
      exact: !1,
      message: r.minLength.message
    }), s.dirty()), r.maxLength !== null && n.data.length > r.maxLength.value && (f(n, {
      code: u.too_big,
      maximum: r.maxLength.value,
      type: "array",
      inclusive: !0,
      exact: !1,
      message: r.maxLength.message
    }), s.dirty()), n.common.async)
      return Promise.all([...n.data].map((i, o) => r.type._parseAsync(new te(n, i, n.path, o)))).then((i) => D.mergeArray(s, i));
    const a = [...n.data].map((i, o) => r.type._parseSync(new te(n, i, n.path, o)));
    return D.mergeArray(s, a);
  }
  get element() {
    return this._def.type;
  }
  min(e, n) {
    return new H({
      ...this._def,
      minLength: { value: e, message: m.toString(n) }
    });
  }
  max(e, n) {
    return new H({
      ...this._def,
      maxLength: { value: e, message: m.toString(n) }
    });
  }
  length(e, n) {
    return new H({
      ...this._def,
      exactLength: { value: e, message: m.toString(n) }
    });
  }
  nonempty(e) {
    return this.min(1, e);
  }
}
H.create = (t, e) => new H({
  type: t,
  minLength: null,
  maxLength: null,
  exactLength: null,
  typeName: k.ZodArray,
  ...S(e)
});
function le(t) {
  if (t instanceof $) {
    const e = {};
    for (const n in t.shape) {
      const s = t.shape[n];
      e[n] = ee.create(le(s));
    }
    return new $({
      ...t._def,
      shape: () => e
    });
  } else return t instanceof H ? new H({
    ...t._def,
    type: le(t.element)
  }) : t instanceof ee ? ee.create(le(t.unwrap())) : t instanceof ge ? ge.create(le(t.unwrap())) : t instanceof ie ? ie.create(t.items.map((e) => le(e))) : t;
}
class $ extends R {
  constructor() {
    super(...arguments), this._cached = null, this.nonstrict = this.passthrough, this.augment = this.extend;
  }
  _getCached() {
    if (this._cached !== null)
      return this._cached;
    const e = this._def.shape(), n = T.objectKeys(e);
    return this._cached = { shape: e, keys: n }, this._cached;
  }
  _parse(e) {
    if (this._getType(e) !== p.object) {
      const d = this._getOrReturnCtx(e);
      return f(d, {
        code: u.invalid_type,
        expected: p.object,
        received: d.parsedType
      }), b;
    }
    const { status: s, ctx: r } = this._processInputParams(e), { shape: a, keys: i } = this._getCached(), o = [];
    if (!(this._def.catchall instanceof ne && this._def.unknownKeys === "strip"))
      for (const d in r.data)
        i.includes(d) || o.push(d);
    const c = [];
    for (const d of i) {
      const h = a[d], v = r.data[d];
      c.push({
        key: { status: "valid", value: d },
        value: h._parse(new te(r, v, r.path, d)),
        alwaysSet: d in r.data
      });
    }
    if (this._def.catchall instanceof ne) {
      const d = this._def.unknownKeys;
      if (d === "passthrough")
        for (const h of o)
          c.push({
            key: { status: "valid", value: h },
            value: { status: "valid", value: r.data[h] }
          });
      else if (d === "strict")
        o.length > 0 && (f(r, {
          code: u.unrecognized_keys,
          keys: o
        }), s.dirty());
      else if (d !== "strip") throw new Error("Internal ZodObject error: invalid unknownKeys value.");
    } else {
      const d = this._def.catchall;
      for (const h of o) {
        const v = r.data[h];
        c.push({
          key: { status: "valid", value: h },
          value: d._parse(
            new te(r, v, r.path, h)
            //, ctx.child(key), value, getParsedType(value)
          ),
          alwaysSet: h in r.data
        });
      }
    }
    return r.common.async ? Promise.resolve().then(async () => {
      const d = [];
      for (const h of c) {
        const v = await h.key, g = await h.value;
        d.push({
          key: v,
          value: g,
          alwaysSet: h.alwaysSet
        });
      }
      return d;
    }).then((d) => D.mergeObjectSync(s, d)) : D.mergeObjectSync(s, c);
  }
  get shape() {
    return this._def.shape();
  }
  strict(e) {
    return m.errToObj, new $({
      ...this._def,
      unknownKeys: "strict",
      ...e !== void 0 ? {
        errorMap: (n, s) => {
          var a, i;
          const r = ((i = (a = this._def).errorMap) == null ? void 0 : i.call(a, n, s).message) ?? s.defaultError;
          return n.code === "unrecognized_keys" ? {
            message: m.errToObj(e).message ?? r
          } : {
            message: r
          };
        }
      } : {}
    });
  }
  strip() {
    return new $({
      ...this._def,
      unknownKeys: "strip"
    });
  }
  passthrough() {
    return new $({
      ...this._def,
      unknownKeys: "passthrough"
    });
  }
  // const AugmentFactory =
  //   <Def extends ZodObjectDef>(def: Def) =>
  //   <Augmentation extends ZodRawShape>(
  //     augmentation: Augmentation
  //   ): ZodObject<
  //     extendShape<ReturnType<Def["shape"]>, Augmentation>,
  //     Def["unknownKeys"],
  //     Def["catchall"]
  //   > => {
  //     return new ZodObject({
  //       ...def,
  //       shape: () => ({
  //         ...def.shape(),
  //         ...augmentation,
  //       }),
  //     }) as any;
  //   };
  extend(e) {
    return new $({
      ...this._def,
      shape: () => ({
        ...this._def.shape(),
        ...e
      })
    });
  }
  /**
   * Prior to zod@1.0.12 there was a bug in the
   * inferred type of merged objects. Please
   * upgrade if you are experiencing issues.
   */
  merge(e) {
    return new $({
      unknownKeys: e._def.unknownKeys,
      catchall: e._def.catchall,
      shape: () => ({
        ...this._def.shape(),
        ...e._def.shape()
      }),
      typeName: k.ZodObject
    });
  }
  // merge<
  //   Incoming extends AnyZodObject,
  //   Augmentation extends Incoming["shape"],
  //   NewOutput extends {
  //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation
  //       ? Augmentation[k]["_output"]
  //       : k extends keyof Output
  //       ? Output[k]
  //       : never;
  //   },
  //   NewInput extends {
  //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation
  //       ? Augmentation[k]["_input"]
  //       : k extends keyof Input
  //       ? Input[k]
  //       : never;
  //   }
  // >(
  //   merging: Incoming
  // ): ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"],
  //   NewOutput,
  //   NewInput
  // > {
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  setKey(e, n) {
    return this.augment({ [e]: n });
  }
  // merge<Incoming extends AnyZodObject>(
  //   merging: Incoming
  // ): //ZodObject<T & Incoming["_shape"], UnknownKeys, Catchall> = (merging) => {
  // ZodObject<
  //   extendShape<T, ReturnType<Incoming["_def"]["shape"]>>,
  //   Incoming["_def"]["unknownKeys"],
  //   Incoming["_def"]["catchall"]
  // > {
  //   // const mergedShape = objectUtil.mergeShapes(
  //   //   this._def.shape(),
  //   //   merging._def.shape()
  //   // );
  //   const merged: any = new ZodObject({
  //     unknownKeys: merging._def.unknownKeys,
  //     catchall: merging._def.catchall,
  //     shape: () =>
  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),
  //     typeName: ZodFirstPartyTypeKind.ZodObject,
  //   }) as any;
  //   return merged;
  // }
  catchall(e) {
    return new $({
      ...this._def,
      catchall: e
    });
  }
  pick(e) {
    const n = {};
    for (const s of T.objectKeys(e))
      e[s] && this.shape[s] && (n[s] = this.shape[s]);
    return new $({
      ...this._def,
      shape: () => n
    });
  }
  omit(e) {
    const n = {};
    for (const s of T.objectKeys(this.shape))
      e[s] || (n[s] = this.shape[s]);
    return new $({
      ...this._def,
      shape: () => n
    });
  }
  /**
   * @deprecated
   */
  deepPartial() {
    return le(this);
  }
  partial(e) {
    const n = {};
    for (const s of T.objectKeys(this.shape)) {
      const r = this.shape[s];
      e && !e[s] ? n[s] = r : n[s] = r.optional();
    }
    return new $({
      ...this._def,
      shape: () => n
    });
  }
  required(e) {
    const n = {};
    for (const s of T.objectKeys(this.shape))
      if (e && !e[s])
        n[s] = this.shape[s];
      else {
        let a = this.shape[s];
        for (; a instanceof ee; )
          a = a._def.innerType;
        n[s] = a;
      }
    return new $({
      ...this._def,
      shape: () => n
    });
  }
  keyof() {
    return Mt(T.objectKeys(this.shape));
  }
}
$.create = (t, e) => new $({
  shape: () => t,
  unknownKeys: "strip",
  catchall: ne.create(),
  typeName: k.ZodObject,
  ...S(e)
});
$.strictCreate = (t, e) => new $({
  shape: () => t,
  unknownKeys: "strict",
  catchall: ne.create(),
  typeName: k.ZodObject,
  ...S(e)
});
$.lazycreate = (t, e) => new $({
  shape: t,
  unknownKeys: "strip",
  catchall: ne.create(),
  typeName: k.ZodObject,
  ...S(e)
});
class Ze extends R {
  _parse(e) {
    const { ctx: n } = this._processInputParams(e), s = this._def.options;
    function r(a) {
      for (const o of a)
        if (o.result.status === "valid")
          return o.result;
      for (const o of a)
        if (o.result.status === "dirty")
          return n.common.issues.push(...o.ctx.common.issues), o.result;
      const i = a.map((o) => new X(o.ctx.common.issues));
      return f(n, {
        code: u.invalid_union,
        unionErrors: i
      }), b;
    }
    if (n.common.async)
      return Promise.all(s.map(async (a) => {
        const i = {
          ...n,
          common: {
            ...n.common,
            issues: []
          },
          parent: null
        };
        return {
          result: await a._parseAsync({
            data: n.data,
            path: n.path,
            parent: i
          }),
          ctx: i
        };
      })).then(r);
    {
      let a;
      const i = [];
      for (const c of s) {
        const d = {
          ...n,
          common: {
            ...n.common,
            issues: []
          },
          parent: null
        }, h = c._parseSync({
          data: n.data,
          path: n.path,
          parent: d
        });
        if (h.status === "valid")
          return h;
        h.status === "dirty" && !a && (a = { result: h, ctx: d }), d.common.issues.length && i.push(d.common.issues);
      }
      if (a)
        return n.common.issues.push(...a.ctx.common.issues), a.result;
      const o = i.map((c) => new X(c));
      return f(n, {
        code: u.invalid_union,
        unionErrors: o
      }), b;
    }
  }
  get options() {
    return this._def.options;
  }
}
Ze.create = (t, e) => new Ze({
  options: t,
  typeName: k.ZodUnion,
  ...S(e)
});
function Ge(t, e) {
  const n = Q(t), s = Q(e);
  if (t === e)
    return { valid: !0, data: t };
  if (n === p.object && s === p.object) {
    const r = T.objectKeys(e), a = T.objectKeys(t).filter((o) => r.indexOf(o) !== -1), i = { ...t, ...e };
    for (const o of a) {
      const c = Ge(t[o], e[o]);
      if (!c.valid)
        return { valid: !1 };
      i[o] = c.data;
    }
    return { valid: !0, data: i };
  } else if (n === p.array && s === p.array) {
    if (t.length !== e.length)
      return { valid: !1 };
    const r = [];
    for (let a = 0; a < t.length; a++) {
      const i = t[a], o = e[a], c = Ge(i, o);
      if (!c.valid)
        return { valid: !1 };
      r.push(c.data);
    }
    return { valid: !0, data: r };
  } else return n === p.date && s === p.date && +t == +e ? { valid: !0, data: t } : { valid: !1 };
}
class Me extends R {
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e), r = (a, i) => {
      if (pt(a) || pt(i))
        return b;
      const o = Ge(a.value, i.value);
      return o.valid ? ((mt(a) || mt(i)) && n.dirty(), { status: n.value, value: o.data }) : (f(s, {
        code: u.invalid_intersection_types
      }), b);
    };
    return s.common.async ? Promise.all([
      this._def.left._parseAsync({
        data: s.data,
        path: s.path,
        parent: s
      }),
      this._def.right._parseAsync({
        data: s.data,
        path: s.path,
        parent: s
      })
    ]).then(([a, i]) => r(a, i)) : r(this._def.left._parseSync({
      data: s.data,
      path: s.path,
      parent: s
    }), this._def.right._parseSync({
      data: s.data,
      path: s.path,
      parent: s
    }));
  }
}
Me.create = (t, e, n) => new Me({
  left: t,
  right: e,
  typeName: k.ZodIntersection,
  ...S(n)
});
class ie extends R {
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e);
    if (s.parsedType !== p.array)
      return f(s, {
        code: u.invalid_type,
        expected: p.array,
        received: s.parsedType
      }), b;
    if (s.data.length < this._def.items.length)
      return f(s, {
        code: u.too_small,
        minimum: this._def.items.length,
        inclusive: !0,
        exact: !1,
        type: "array"
      }), b;
    !this._def.rest && s.data.length > this._def.items.length && (f(s, {
      code: u.too_big,
      maximum: this._def.items.length,
      inclusive: !0,
      exact: !1,
      type: "array"
    }), n.dirty());
    const a = [...s.data].map((i, o) => {
      const c = this._def.items[o] || this._def.rest;
      return c ? c._parse(new te(s, i, s.path, o)) : null;
    }).filter((i) => !!i);
    return s.common.async ? Promise.all(a).then((i) => D.mergeArray(n, i)) : D.mergeArray(n, a);
  }
  get items() {
    return this._def.items;
  }
  rest(e) {
    return new ie({
      ...this._def,
      rest: e
    });
  }
}
ie.create = (t, e) => {
  if (!Array.isArray(t))
    throw new Error("You must pass an array of schemas to z.tuple([ ... ])");
  return new ie({
    items: t,
    typeName: k.ZodTuple,
    rest: null,
    ...S(e)
  });
};
class wt extends R {
  get keySchema() {
    return this._def.keyType;
  }
  get valueSchema() {
    return this._def.valueType;
  }
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e);
    if (s.parsedType !== p.map)
      return f(s, {
        code: u.invalid_type,
        expected: p.map,
        received: s.parsedType
      }), b;
    const r = this._def.keyType, a = this._def.valueType, i = [...s.data.entries()].map(([o, c], d) => ({
      key: r._parse(new te(s, o, s.path, [d, "key"])),
      value: a._parse(new te(s, c, s.path, [d, "value"]))
    }));
    if (s.common.async) {
      const o = /* @__PURE__ */ new Map();
      return Promise.resolve().then(async () => {
        for (const c of i) {
          const d = await c.key, h = await c.value;
          if (d.status === "aborted" || h.status === "aborted")
            return b;
          (d.status === "dirty" || h.status === "dirty") && n.dirty(), o.set(d.value, h.value);
        }
        return { status: n.value, value: o };
      });
    } else {
      const o = /* @__PURE__ */ new Map();
      for (const c of i) {
        const d = c.key, h = c.value;
        if (d.status === "aborted" || h.status === "aborted")
          return b;
        (d.status === "dirty" || h.status === "dirty") && n.dirty(), o.set(d.value, h.value);
      }
      return { status: n.value, value: o };
    }
  }
}
wt.create = (t, e, n) => new wt({
  valueType: e,
  keyType: t,
  typeName: k.ZodMap,
  ...S(n)
});
class Se extends R {
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e);
    if (s.parsedType !== p.set)
      return f(s, {
        code: u.invalid_type,
        expected: p.set,
        received: s.parsedType
      }), b;
    const r = this._def;
    r.minSize !== null && s.data.size < r.minSize.value && (f(s, {
      code: u.too_small,
      minimum: r.minSize.value,
      type: "set",
      inclusive: !0,
      exact: !1,
      message: r.minSize.message
    }), n.dirty()), r.maxSize !== null && s.data.size > r.maxSize.value && (f(s, {
      code: u.too_big,
      maximum: r.maxSize.value,
      type: "set",
      inclusive: !0,
      exact: !1,
      message: r.maxSize.message
    }), n.dirty());
    const a = this._def.valueType;
    function i(c) {
      const d = /* @__PURE__ */ new Set();
      for (const h of c) {
        if (h.status === "aborted")
          return b;
        h.status === "dirty" && n.dirty(), d.add(h.value);
      }
      return { status: n.value, value: d };
    }
    const o = [...s.data.values()].map((c, d) => a._parse(new te(s, c, s.path, d)));
    return s.common.async ? Promise.all(o).then((c) => i(c)) : i(o);
  }
  min(e, n) {
    return new Se({
      ...this._def,
      minSize: { value: e, message: m.toString(n) }
    });
  }
  max(e, n) {
    return new Se({
      ...this._def,
      maxSize: { value: e, message: m.toString(n) }
    });
  }
  size(e, n) {
    return this.min(e, n).max(e, n);
  }
  nonempty(e) {
    return this.min(1, e);
  }
}
Se.create = (t, e) => new Se({
  valueType: t,
  minSize: null,
  maxSize: null,
  typeName: k.ZodSet,
  ...S(e)
});
class St extends R {
  get schema() {
    return this._def.getter();
  }
  _parse(e) {
    const { ctx: n } = this._processInputParams(e);
    return this._def.getter()._parse({ data: n.data, path: n.path, parent: n });
  }
}
St.create = (t, e) => new St({
  getter: t,
  typeName: k.ZodLazy,
  ...S(e)
});
class Rt extends R {
  _parse(e) {
    if (e.data !== this._def.value) {
      const n = this._getOrReturnCtx(e);
      return f(n, {
        received: n.data,
        code: u.invalid_literal,
        expected: this._def.value
      }), b;
    }
    return { status: "valid", value: e.data };
  }
  get value() {
    return this._def.value;
  }
}
Rt.create = (t, e) => new Rt({
  value: t,
  typeName: k.ZodLiteral,
  ...S(e)
});
function Mt(t, e) {
  return new pe({
    values: t,
    typeName: k.ZodEnum,
    ...S(e)
  });
}
class pe extends R {
  _parse(e) {
    if (typeof e.data != "string") {
      const n = this._getOrReturnCtx(e), s = this._def.values;
      return f(n, {
        expected: T.joinValues(s),
        received: n.parsedType,
        code: u.invalid_type
      }), b;
    }
    if (this._cache || (this._cache = new Set(this._def.values)), !this._cache.has(e.data)) {
      const n = this._getOrReturnCtx(e), s = this._def.values;
      return f(n, {
        received: n.data,
        code: u.invalid_enum_value,
        options: s
      }), b;
    }
    return z(e.data);
  }
  get options() {
    return this._def.values;
  }
  get enum() {
    const e = {};
    for (const n of this._def.values)
      e[n] = n;
    return e;
  }
  get Values() {
    const e = {};
    for (const n of this._def.values)
      e[n] = n;
    return e;
  }
  get Enum() {
    const e = {};
    for (const n of this._def.values)
      e[n] = n;
    return e;
  }
  extract(e, n = this._def) {
    return pe.create(e, {
      ...this._def,
      ...n
    });
  }
  exclude(e, n = this._def) {
    return pe.create(this.options.filter((s) => !e.includes(s)), {
      ...this._def,
      ...n
    });
  }
}
pe.create = Mt;
class Tt extends R {
  _parse(e) {
    const n = T.getValidEnumValues(this._def.values), s = this._getOrReturnCtx(e);
    if (s.parsedType !== p.string && s.parsedType !== p.number) {
      const r = T.objectValues(n);
      return f(s, {
        expected: T.joinValues(r),
        received: s.parsedType,
        code: u.invalid_type
      }), b;
    }
    if (this._cache || (this._cache = new Set(T.getValidEnumValues(this._def.values))), !this._cache.has(e.data)) {
      const r = T.objectValues(n);
      return f(s, {
        received: s.data,
        code: u.invalid_enum_value,
        options: r
      }), b;
    }
    return z(e.data);
  }
  get enum() {
    return this._def.values;
  }
}
Tt.create = (t, e) => new Tt({
  values: t,
  typeName: k.ZodNativeEnum,
  ...S(e)
});
class We extends R {
  unwrap() {
    return this._def.type;
  }
  _parse(e) {
    const { ctx: n } = this._processInputParams(e);
    if (n.parsedType !== p.promise && n.common.async === !1)
      return f(n, {
        code: u.invalid_type,
        expected: p.promise,
        received: n.parsedType
      }), b;
    const s = n.parsedType === p.promise ? n.data : Promise.resolve(n.data);
    return z(s.then((r) => this._def.type.parseAsync(r, {
      path: n.path,
      errorMap: n.common.contextualErrorMap
    })));
  }
}
We.create = (t, e) => new We({
  type: t,
  typeName: k.ZodPromise,
  ...S(e)
});
class me extends R {
  innerType() {
    return this._def.schema;
  }
  sourceType() {
    return this._def.schema._def.typeName === k.ZodEffects ? this._def.schema.sourceType() : this._def.schema;
  }
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e), r = this._def.effect || null, a = {
      addIssue: (i) => {
        f(s, i), i.fatal ? n.abort() : n.dirty();
      },
      get path() {
        return s.path;
      }
    };
    if (a.addIssue = a.addIssue.bind(a), r.type === "preprocess") {
      const i = r.transform(s.data, a);
      if (s.common.async)
        return Promise.resolve(i).then(async (o) => {
          if (n.value === "aborted")
            return b;
          const c = await this._def.schema._parseAsync({
            data: o,
            path: s.path,
            parent: s
          });
          return c.status === "aborted" ? b : c.status === "dirty" || n.value === "dirty" ? ke(c.value) : c;
        });
      {
        if (n.value === "aborted")
          return b;
        const o = this._def.schema._parseSync({
          data: i,
          path: s.path,
          parent: s
        });
        return o.status === "aborted" ? b : o.status === "dirty" || n.value === "dirty" ? ke(o.value) : o;
      }
    }
    if (r.type === "refinement") {
      const i = (o) => {
        const c = r.refinement(o, a);
        if (s.common.async)
          return Promise.resolve(c);
        if (c instanceof Promise)
          throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");
        return o;
      };
      if (s.common.async === !1) {
        const o = this._def.schema._parseSync({
          data: s.data,
          path: s.path,
          parent: s
        });
        return o.status === "aborted" ? b : (o.status === "dirty" && n.dirty(), i(o.value), { status: n.value, value: o.value });
      } else
        return this._def.schema._parseAsync({ data: s.data, path: s.path, parent: s }).then((o) => o.status === "aborted" ? b : (o.status === "dirty" && n.dirty(), i(o.value).then(() => ({ status: n.value, value: o.value }))));
    }
    if (r.type === "transform")
      if (s.common.async === !1) {
        const i = this._def.schema._parseSync({
          data: s.data,
          path: s.path,
          parent: s
        });
        if (!he(i))
          return b;
        const o = r.transform(i.value, a);
        if (o instanceof Promise)
          throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");
        return { status: n.value, value: o };
      } else
        return this._def.schema._parseAsync({ data: s.data, path: s.path, parent: s }).then((i) => he(i) ? Promise.resolve(r.transform(i.value, a)).then((o) => ({
          status: n.value,
          value: o
        })) : b);
    T.assertNever(r);
  }
}
me.create = (t, e, n) => new me({
  schema: t,
  typeName: k.ZodEffects,
  effect: e,
  ...S(n)
});
me.createWithPreprocess = (t, e, n) => new me({
  schema: e,
  effect: { type: "preprocess", transform: t },
  typeName: k.ZodEffects,
  ...S(n)
});
class ee extends R {
  _parse(e) {
    return this._getType(e) === p.undefined ? z(void 0) : this._def.innerType._parse(e);
  }
  unwrap() {
    return this._def.innerType;
  }
}
ee.create = (t, e) => new ee({
  innerType: t,
  typeName: k.ZodOptional,
  ...S(e)
});
class ge extends R {
  _parse(e) {
    return this._getType(e) === p.null ? z(null) : this._def.innerType._parse(e);
  }
  unwrap() {
    return this._def.innerType;
  }
}
ge.create = (t, e) => new ge({
  innerType: t,
  typeName: k.ZodNullable,
  ...S(e)
});
class Je extends R {
  _parse(e) {
    const { ctx: n } = this._processInputParams(e);
    let s = n.data;
    return n.parsedType === p.undefined && (s = this._def.defaultValue()), this._def.innerType._parse({
      data: s,
      path: n.path,
      parent: n
    });
  }
  removeDefault() {
    return this._def.innerType;
  }
}
Je.create = (t, e) => new Je({
  innerType: t,
  typeName: k.ZodDefault,
  defaultValue: typeof e.default == "function" ? e.default : () => e.default,
  ...S(e)
});
class Qe extends R {
  _parse(e) {
    const { ctx: n } = this._processInputParams(e), s = {
      ...n,
      common: {
        ...n.common,
        issues: []
      }
    }, r = this._def.innerType._parse({
      data: s.data,
      path: s.path,
      parent: {
        ...s
      }
    });
    return Pe(r) ? r.then((a) => ({
      status: "valid",
      value: a.status === "valid" ? a.value : this._def.catchValue({
        get error() {
          return new X(s.common.issues);
        },
        input: s.data
      })
    })) : {
      status: "valid",
      value: r.status === "valid" ? r.value : this._def.catchValue({
        get error() {
          return new X(s.common.issues);
        },
        input: s.data
      })
    };
  }
  removeCatch() {
    return this._def.innerType;
  }
}
Qe.create = (t, e) => new Qe({
  innerType: t,
  typeName: k.ZodCatch,
  catchValue: typeof e.catch == "function" ? e.catch : () => e.catch,
  ...S(e)
});
class Ct extends R {
  _parse(e) {
    if (this._getType(e) !== p.nan) {
      const s = this._getOrReturnCtx(e);
      return f(s, {
        code: u.invalid_type,
        expected: p.nan,
        received: s.parsedType
      }), b;
    }
    return { status: "valid", value: e.data };
  }
}
Ct.create = (t) => new Ct({
  typeName: k.ZodNaN,
  ...S(t)
});
class Un extends R {
  _parse(e) {
    const { ctx: n } = this._processInputParams(e), s = n.data;
    return this._def.type._parse({
      data: s,
      path: n.path,
      parent: n
    });
  }
  unwrap() {
    return this._def.type;
  }
}
class nt extends R {
  _parse(e) {
    const { status: n, ctx: s } = this._processInputParams(e);
    if (s.common.async)
      return (async () => {
        const a = await this._def.in._parseAsync({
          data: s.data,
          path: s.path,
          parent: s
        });
        return a.status === "aborted" ? b : a.status === "dirty" ? (n.dirty(), ke(a.value)) : this._def.out._parseAsync({
          data: a.value,
          path: s.path,
          parent: s
        });
      })();
    {
      const r = this._def.in._parseSync({
        data: s.data,
        path: s.path,
        parent: s
      });
      return r.status === "aborted" ? b : r.status === "dirty" ? (n.dirty(), {
        status: "dirty",
        value: r.value
      }) : this._def.out._parseSync({
        data: r.value,
        path: s.path,
        parent: s
      });
    }
  }
  static create(e, n) {
    return new nt({
      in: e,
      out: n,
      typeName: k.ZodPipeline
    });
  }
}
class Ke extends R {
  _parse(e) {
    const n = this._def.innerType._parse(e), s = (r) => (he(r) && (r.value = Object.freeze(r.value)), r);
    return Pe(n) ? n.then((r) => s(r)) : s(n);
  }
  unwrap() {
    return this._def.innerType;
  }
}
Ke.create = (t, e) => new Ke({
  innerType: t,
  typeName: k.ZodReadonly,
  ...S(e)
});
var k;
(function(t) {
  t.ZodString = "ZodString", t.ZodNumber = "ZodNumber", t.ZodNaN = "ZodNaN", t.ZodBigInt = "ZodBigInt", t.ZodBoolean = "ZodBoolean", t.ZodDate = "ZodDate", t.ZodSymbol = "ZodSymbol", t.ZodUndefined = "ZodUndefined", t.ZodNull = "ZodNull", t.ZodAny = "ZodAny", t.ZodUnknown = "ZodUnknown", t.ZodNever = "ZodNever", t.ZodVoid = "ZodVoid", t.ZodArray = "ZodArray", t.ZodObject = "ZodObject", t.ZodUnion = "ZodUnion", t.ZodDiscriminatedUnion = "ZodDiscriminatedUnion", t.ZodIntersection = "ZodIntersection", t.ZodTuple = "ZodTuple", t.ZodRecord = "ZodRecord", t.ZodMap = "ZodMap", t.ZodSet = "ZodSet", t.ZodFunction = "ZodFunction", t.ZodLazy = "ZodLazy", t.ZodLiteral = "ZodLiteral", t.ZodEnum = "ZodEnum", t.ZodEffects = "ZodEffects", t.ZodNativeEnum = "ZodNativeEnum", t.ZodOptional = "ZodOptional", t.ZodNullable = "ZodNullable", t.ZodDefault = "ZodDefault", t.ZodCatch = "ZodCatch", t.ZodPromise = "ZodPromise", t.ZodBranded = "ZodBranded", t.ZodPipeline = "ZodPipeline", t.ZodReadonly = "ZodReadonly";
})(k || (k = {}));
const De = K.create, je = fe.create, Yn = qe.create;
ne.create;
const Hn = H.create, st = $.create;
Ze.create;
Me.create;
ie.create;
pe.create;
We.create;
ee.create;
ge.create;
var re = /* @__PURE__ */ ((t) => (t.STORYBOARD = "storyboard", t.TEXT = "text", t.VIDEO = "video", t.SOUND = "sound", t.NARRATION = "NARRATION", t.CAPTION = "caption", t.STICKER = "sticker", t.material = "material", t))(re || {});
const Xn = st({
  overlays: Hn(Yn()),
  // Replace with your actual Overlay type
  durationInFrames: je(),
  width: je(),
  height: je(),
  fps: je(),
  src: De()
});
st({
  id: De(),
  inputProps: Xn
});
st({
  bucketName: De(),
  id: De()
});
const Ie = {
  width: "100%",
  height: "100%"
}, Ot = ({
  overlay: t,
  baseUrl: e
}) => {
  switch (t.type) {
    case re.VIDEO:
      return /* @__PURE__ */ _.jsx("div", { style: { ...Ie }, children: /* @__PURE__ */ _.jsx(_n, { overlay: t, baseUrl: e }) });
    case re.TEXT:
      return /* @__PURE__ */ _.jsx("div", { style: { ...Ie }, children: /* @__PURE__ */ _.jsx(xn, { overlay: t }) });
    case re.CAPTION:
      return /* @__PURE__ */ _.jsx(
        "div",
        {
          style: {
            ...Ie,
            position: "relative",
            overflow: "hidden",
            display: "flex"
          },
          children: /* @__PURE__ */ _.jsx(Kt, { overlay: t })
        }
      );
    case re.STICKER:
      return /* @__PURE__ */ _.jsx("div", { style: { ...Ie }, className: "layer-content-wrapper", children: /* @__PURE__ */ _.jsx(sn, { overlay: t }) });
    case re.SOUND:
      return /* @__PURE__ */ _.jsx(en, { overlay: t, baseUrl: e });
    default:
      return null;
  }
}, qn = ({ overlay: t, baseUrl: e }) => {
  const n = Ye(() => ({
    position: "absolute",
    left: t.left,
    top: t.top,
    width: t.width,
    height: t.height,
    transform: `rotate(${t.rotation || 0}deg)`,
    transformOrigin: "center center",
    zIndex: t.zIndex
  }), [t]);
  return t.type === re.STORYBOARD ? null : t.type === "sound" ? /* @__PURE__ */ _.jsx(
    at,
    {
      from: t.from,
      durationInFrames: t.durationInFrames,
      children: /* @__PURE__ */ _.jsx(Ot, { overlay: t, baseUrl: e })
    },
    t.id
  ) : /* @__PURE__ */ _.jsx(Bt, { children: /* @__PURE__ */ _.jsx(
    Nt,
    {
      style: {
        overflow: "hidden",
        maxWidth: "3000px"
      },
      children: /* @__PURE__ */ _.jsx(
        at,
        {
          from: t.from,
          durationInFrames: t.durationInFrames,
          layout: "none",
          children: /* @__PURE__ */ _.jsx("div", { style: n, children: /* @__PURE__ */ _.jsx(Ot, { overlay: t, baseUrl: e }) })
        },
        t.id
      )
    }
  ) });
}, Gn = ({
  overlays: t,
  baseUrl: e,
  playerMetadata: n
}) => {
  if (!n)
    throw new Error("Unable to load player metadata");
  return /* @__PURE__ */ _.jsx(
    Lt.Provider,
    {
      value: {
        overlays: t,
        baseUrl: e,
        playerMetadata: n
      },
      children: /* @__PURE__ */ _.jsx(Nt, { style: { backgroundColor: "#000000" }, children: t.map((s) => /* @__PURE__ */ _.jsx(
        qn,
        {
          overlay: s,
          baseUrl: e
        },
        s.id
      )) })
    }
  );
}, Et = 0, At = 30, Jn = "V0-0-1", Qn = () => {
  const t = {
    overlays: [],
    durationInFrames: Et,
    fps: At,
    width: 1920,
    height: 1920,
    src: "",
    setSelectedOverlayId: () => {
    },
    selectedOverlayId: null,
    changeOverlay: () => {
    }
  };
  return /* @__PURE__ */ _.jsx(_.Fragment, { children: /* @__PURE__ */ _.jsx(
    Xt,
    {
      id: Jn,
      component: Gn,
      durationInFrames: Et,
      fps: At,
      width: 1920,
      height: 1920,
      calculateMetadata: async ({ props: e }) => {
        const { durationInFrames: n, width: s, height: r } = e.playerMetadata;
        return {
          durationInFrames: n,
          width: s,
          height: r
        };
      },
      defaultProps: t
    }
  ) });
};
Ht(Qn);
