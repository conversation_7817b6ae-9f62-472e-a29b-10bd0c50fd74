// server.ts
import {createServer} from 'http';
import next from 'next';
import {parse} from 'url';

const dev = process.env.NODE_ENV !== 'production';
const app = next({dev});
const handle = app.getRequestHandler();

// 启动 HTTP 服务
app.prepare().then(() => {
    const server = createServer((req, res) => {
        // 必须解析 URL 给 Next.js
        const parsedUrl = parse(req.url!, true);
        handle(req, res, parsedUrl);
    });

    // ========= 设置 HTTP 超时 =========
    server.timeout = 600_000;        // 请求处理超时 10 分钟
    server.headersTimeout = 610_000; // headersTimeout 必须大于 timeout
    server.keepAliveTimeout = 610_000; // 可选：keep-alive 超时 610 秒
    // =================================

    server.listen(3000, async (err?: Error) => {
        if (err) throw err;
        console.log('✅ Next.js running on http://localhost:3000');
    });
});
